const express = require('express');
const cookieParser = require('cookie-parser');
const path = require('path');
require('dotenv').config();

const { initDatabase } = require('./utils/database');
const authRoutes = require('./routes/auth');
const staffRoutes = require('./routes/staff');
const directorRoutes = require('./routes/director');
const adminRoutes = require('./routes/admin');
const { verifyToken } = require('./middleware/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize database
initDatabase();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, '../public')));

// 模板文件下载路由
app.get('/templates/:filename', (req, res) => {
  const filename = req.params.filename;
  const allowedFiles = ['user_import_template.xlsx', 'duty_import_template.xlsx'];

  if (!allowedFiles.includes(filename)) {
    return res.status(404).send('模板文件不存在');
  }

  const filePath = path.join(__dirname, '../public/templates', filename);
  res.download(filePath, filename);
});

// View engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, '../views'));

// Routes
app.use('/', authRoutes);
app.use('/staff', verifyToken, staffRoutes);
app.use('/director', verifyToken, directorRoutes);
app.use('/admin', verifyToken, adminRoutes);

// Home route - redirect based on current role
app.get('/', verifyToken, (req, res) => {
  switch (req.user.role) {
    case 'admin':
      return res.redirect('/admin/dashboard');
    case 'director':
      return res.redirect('/director/dashboard');
    case 'staff':
      return res.redirect('/staff/dashboard');
    default:
      return res.redirect('/login');
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).render('404', { title: '页面未找到' });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render('error', { 
    title: '系统错误',
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});