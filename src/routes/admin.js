const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const DutyRecord = require('../models/DutyRecord');
const User = require('../models/User');
const { isAdmin } = require('../middleware/auth');
const { importUsers } = require('../utils/excel');
const { validatePasswordStrength } = require('../utils/passwordValidator');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    // 只允许Excel文件 - 检查文件扩展名而不是MIME类型
    const allowedExtensions = ['.xlsx', '.xls'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式 (.xlsx, .xls)'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  }
});

// Admin dashboard
router.get('/dashboard', isAdmin, async (req, res) => {
  try {
    // 获取查询参数，如果没有则使用当前年月
    const currentYear = parseInt(req.query.year) || new Date().getFullYear();
    const currentMonth = parseInt(req.query.month) || new Date().getMonth() + 1;

    // Get all duty records for specified year and month
    const records = await DutyRecord.getAll(currentYear, currentMonth);

    // Get all users with their roles
    const allUsers = await User.getAll();

    // Add role information for each user
    const usersWithRoles = await Promise.all(
      allUsers.map(async (user) => {
        const roles = await User.getUserRoles(user.userid);
        const primaryRole = roles.find(r => r.is_primary);
        const firstRole = roles[0];

        return {
          ...user,
          roles: roles,
          availableRoles: roles.map(r => r.role),
          primaryRole: primaryRole ? primaryRole.role : (firstRole ? firstRole.role : null),
          roleCount: roles.length
        };
      })
    );

    // Calculate statistics based on primary roles
    const staff = usersWithRoles.filter(u => u.primaryRole === 'staff');
    const directors = usersWithRoles.filter(u => u.primaryRole === 'director');
    const admins = usersWithRoles.filter(u => u.primaryRole === 'admin');

    // Calculate multi-role statistics
    const multiRoleUsers = usersWithRoles.filter(u => u.roleCount > 1);

    // Calculate system statistics
    const totalDutyDays = records.length;
    const uniqueStaff = [...new Set(records.map(r => r.userid))].length;
    const avgDutyPerStaff = uniqueStaff > 0 ? (totalDutyDays / uniqueStaff).toFixed(1) : 0;

    res.render('admin/dashboard', {
      title: '系统管理面板',
      user: req.user,
      records,
      users: {
        all: usersWithRoles,
        staff,
        directors,
        admins,
        multiRole: multiRoleUsers
      },
      stats: {
        totalDutyDays,
        uniqueStaff,
        avgDutyPerStaff,
        totalUsers: allUsers.length,
        totalStaff: staff.length,
        totalDirectors: directors.length,
        totalAdmins: admins.length
      },
      currentYear,
      currentMonth
    });
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).send('获取数据时发生错误');
  }
});

// User management routes

// User management page
router.get('/users-page', isAdmin, async (req, res) => {
  try {
    // Get all users with their roles
    const allUsers = await User.getAll();

    // Add role information for each user
    const usersWithRoles = await Promise.all(
      allUsers.map(async (user) => {
        const roles = await User.getUserRoles(user.userid);
        const primaryRole = roles.find(r => r.is_primary);
        const firstRole = roles[0];

        return {
          ...user,
          roles: roles,
          availableRoles: roles.map(r => r.role),
          primaryRole: primaryRole ? primaryRole.role : (firstRole ? firstRole.role : null),
          roleCount: roles.length
        };
      })
    );

    // Calculate statistics based on primary roles
    const staff = usersWithRoles.filter(u => u.primaryRole === 'staff');
    const directors = usersWithRoles.filter(u => u.primaryRole === 'director');
    const admins = usersWithRoles.filter(u => u.primaryRole === 'admin');

    // Calculate multi-role statistics
    const multiRoleUsers = usersWithRoles.filter(u => u.roleCount > 1);

    res.render('admin/users', {
      title: '用户管理',
      user: req.user,
      users: {
        all: usersWithRoles,
        staff,
        directors,
        admins,
        multiRole: multiRoleUsers
      }
    });
  } catch (error) {
    console.error('Users page error:', error);
    res.status(500).send('获取用户数据时发生错误');
  }
});

// Get all users (API)
router.get('/users', isAdmin, async (req, res) => {
  try {
    const users = await User.getAll();
    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: '获取用户列表时发生错误' });
  }
});

// Get user details with roles
router.get('/users/:userid', isAdmin, async (req, res) => {
  try {
    const { userid } = req.params;
    const user = await User.getById(userid);

    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    const roles = await User.getUserRoles(userid);

    const primaryRole = roles.find(r => r.is_primary);
    const firstRole = roles[0];

    res.json({
      ...user,
      roles: roles,
      availableRoles: roles.map(r => r.role),
      primaryRole: primaryRole ? primaryRole.role : (firstRole ? firstRole.role : null)
    });
  } catch (error) {
    console.error('Get user details error:', error);
    res.status(500).json({ error: '获取用户详情时发生错误' });
  }
});

// Create new user
router.post('/users', isAdmin, async (req, res) => {
  try {
    const { name, jobNumber, roles, password } = req.body;

    // Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: `密码强度不符合要求：${passwordValidation.errors.join('，')}`
      });
    }

    // Check if job number already exists
    const existingUser = await User.getByJobNumber(jobNumber);
    if (existingUser) {
      return res.status(400).json({ error: '工号已存在' });
    }

    // Handle both single role (legacy) and multiple roles
    let userRoles = [];
    if (Array.isArray(roles)) {
      userRoles = roles;
    } else if (roles) {
      userRoles = [roles];
    } else {
      // Fallback to legacy 'role' parameter
      const { role } = req.body;
      userRoles = role ? [role] : ['staff'];
    }

    if (userRoles.length === 0) {
      return res.status(400).json({ error: '至少需要选择一个角色' });
    }

    // Create user with primary role (first role in the array)
    const primaryRole = userRoles[0];
    const userid = await User.create(name, jobNumber, primaryRole, password);

    // Add all roles to user_roles table
    for (let i = 0; i < userRoles.length; i++) {
      const role = userRoles[i];
      const isPrimary = i === 0; // First role is primary
      await User.addUserRole(userid, role, isPrimary);
    }

    res.json({ success: true, userid, message: '用户创建成功' });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ error: '创建用户时发生错误' });
  }
});

// Update user
router.put('/users/:userid', isAdmin, async (req, res) => {
  try {
    const { userid } = req.params;
    const { name, jobNumber, roles } = req.body;

    // Check if job number already exists for other users
    const existingUser = await User.getByJobNumber(jobNumber);
    if (existingUser && existingUser.userid != userid) {
      return res.status(400).json({ error: '工号已被其他用户使用' });
    }

    // Handle both single role (legacy) and multiple roles
    let userRoles = [];
    if (Array.isArray(roles)) {
      userRoles = roles;
    } else if (roles) {
      userRoles = [roles];
    } else {
      // Fallback to legacy 'role' parameter
      const { role } = req.body;
      userRoles = role ? [role] : ['staff'];
    }

    if (userRoles.length === 0) {
      return res.status(400).json({ error: '至少需要选择一个角色' });
    }

    // Update basic user info with primary role
    const primaryRole = userRoles[0];
    await User.update(userid, name, jobNumber, primaryRole);

    // Clear existing roles
    const existingRoles = await User.getUserRoles(userid);
    for (const existingRole of existingRoles) {
      await User.removeUserRole(userid, existingRole.role);
    }

    // Add new roles
    for (let i = 0; i < userRoles.length; i++) {
      const role = userRoles[i];
      const isPrimary = i === 0; // First role is primary
      await User.addUserRole(userid, role, isPrimary);
    }

    res.json({ success: true, message: '用户信息更新成功' });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: '更新用户时发生错误' });
  }
});

// Delete user
router.delete('/users/:userid', isAdmin, async (req, res) => {
  try {
    const { userid } = req.params;
    
    // Don't allow deleting the current admin user
    if (userid == req.user.userid) {
      return res.status(400).json({ error: '不能删除当前登录的管理员账户' });
    }
    
    await User.delete(userid);
    res.json({ success: true, message: '用户删除成功' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: '删除用户时发生错误' });
  }
});

// Reset user password
router.post('/users/:userid/reset-password', isAdmin, async (req, res) => {
  try {
    const { userid } = req.params;
    const { newPassword } = req.body;

    // Validate password strength
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: `密码强度不符合要求：${passwordValidation.errors.join('，')}`
      });
    }

    await User.updatePassword(userid, newPassword);
    res.json({ success: true, message: '密码重置成功' });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ error: '重置密码时发生错误' });
  }
});

// Batch import users from Excel
router.post('/users/import', isAdmin, upload.single('userFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请选择要上传的Excel文件' });
    }

    const filePath = req.file.path;
    console.log('Importing users from file:', filePath);

    // 调用Excel导入功能
    const result = await importUsers(filePath);

    // 删除上传的文件
    const fs = require('fs');
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: `导入完成！成功导入 ${result.success} 个用户`,
      details: {
        successCount: result.success,
        errorCount: result.errors.length,
        errors: result.errors
      }
    });
  } catch (error) {
    console.error('Import users error:', error);

    // 如果文件存在，删除它
    if (req.file && req.file.path) {
      const fs = require('fs');
      try {
        fs.unlinkSync(req.file.path);
      } catch (deleteError) {
        console.error('Error deleting uploaded file:', deleteError);
      }
    }

    res.status(500).json({ error: '导入用户时发生错误: ' + error.message });
  }
});

// Download user import template
router.get('/users/template', isAdmin, (req, res) => {
  try {
    const path = require('path');
    const templatePath = path.join(__dirname, '../../public/templates/user_import_template.xlsx');

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=user_import_template.xlsx');

    // 发送文件
    res.sendFile(templatePath);
  } catch (error) {
    console.error('Download template error:', error);
    res.status(500).json({ error: '下载模板时发生错误' });
  }
});

// Duty record management (admin has full access)

// Get all records for specific month
router.get('/records/:year/:month', isAdmin, async (req, res) => {
  try {
    const { year, month } = req.params;

    const records = await DutyRecord.getAll(parseInt(year), parseInt(month));
    res.json(records);
  } catch (error) {
    console.error('Get monthly records error:', error);
    res.status(500).json({ error: '获取记录时发生错误' });
  }
});

// Create duty record
router.post('/records', isAdmin, async (req, res) => {
  try {
    const { userid, dutyDate, leaveDate, leaveEndDate, isCustomLeave, isHolidayDuty, remarks } = req.body;

    const recordId = await DutyRecord.createWithCustomLeave(userid, dutyDate, leaveDate, isCustomLeave, remarks, leaveEndDate, false, isHolidayDuty);
    res.json({ success: true, recordId, message: '值班记录创建成功' });
  } catch (error) {
    console.error('Create duty record error:', error);
    res.status(500).json({ error: '创建记录时发生错误' });
  }
});

// Update duty record
router.put('/records/:recordId', isAdmin, async (req, res) => {
  try {
    const { recordId } = req.params;
    const { dutyDate, leaveDate, leaveEndDate, isCustomLeave, isHolidayDuty, remarks } = req.body;

    await DutyRecord.updateWithCustomLeave(recordId, dutyDate, leaveDate, isCustomLeave, remarks, leaveEndDate, false, isHolidayDuty);
    res.json({ success: true, message: '记录更新成功' });
  } catch (error) {
    console.error('Update record error:', error);
    res.status(500).json({ error: '更新记录时发生错误' });
  }
});

// Delete duty record
router.delete('/records/:recordId', isAdmin, async (req, res) => {
  try {
    const { recordId } = req.params;

    await DutyRecord.delete(recordId);
    res.json({ success: true, message: '记录删除成功' });
  } catch (error) {
    console.error('Delete record error:', error);
    res.status(500).json({ error: '删除记录时发生错误' });
  }
});

module.exports = router;
