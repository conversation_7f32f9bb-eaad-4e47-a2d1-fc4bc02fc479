const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { verifyToken } = require('../middleware/auth');
const { validatePasswordStrength } = require('../utils/passwordValidator');

// Login page
router.get('/login', (req, res) => {
  res.render('auth/login', { title: '登录', error: null });
});

// Login process
router.post('/login', async (req, res) => {
  try {
    const { job_number, password } = req.body;

    // Find user with roles
    const user = await User.findByJobNumberWithRoles(job_number);
    if (!user) {
      return res.render('auth/login', {
        title: '登录',
        error: '工号或密码不正确'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.render('auth/login', {
        title: '登录',
        error: '工号或密码不正确'
      });
    }

    // 使用主要角色作为默认当前角色
    const currentRole = user.current_role || user.primaryRole;

    // Update current role in database
    await User.updateCurrentRole(user.userid, currentRole);

    // Create token with multi-role support
    const token = jwt.sign(
      {
        userid: user.userid,
        job_number: user.job_number,
        name: user.name,
        role: currentRole,
        availableRoles: user.availableRoles,
        primaryRole: user.primaryRole
      },
      process.env.JWT_SECRET,
      { expiresIn: '1d' }
    );

    // Set cookie and redirect
    res.cookie('token', token, {
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 1 day
    });

    // Redirect based on current role
    switch (currentRole) {
      case 'admin':
        return res.redirect('/admin/dashboard');
      case 'director':
        return res.redirect('/director/dashboard');
      case 'staff':
        return res.redirect('/staff/dashboard');
      default:
        return res.redirect('/');
    }
  } catch (error) {
    console.error('Login error:', error);
    res.render('auth/login', {
      title: '登录',
      error: '登录过程中发生错误'
    });
  }
});

// Change password page
router.get('/change-password', verifyToken, (req, res) => {
  res.render('auth/change-password', { 
    title: '修改密码',
    user: req.user,
    error: null,
    success: null
  });
});

// Change password process
router.post('/change-password', verifyToken, async (req, res) => {
  try {
    const { current_password, new_password, confirm_password } = req.body;

    // Validate passwords
    if (new_password !== confirm_password) {
      return res.render('auth/change-password', {
        title: '修改密码',
        user: req.user,
        error: '新密码与确认密码不匹配',
        success: null
      });
    }

    // Validate password strength
    const passwordValidation = validatePasswordStrength(new_password);
    if (!passwordValidation.isValid) {
      return res.render('auth/change-password', {
        title: '修改密码',
        user: req.user,
        error: `密码强度不符合要求：${passwordValidation.errors.join('，')}`,
        success: null
      });
    }
    
    // Get user
    const user = await User.findById(req.user.userid);
    
    // Check current password
    const isMatch = await bcrypt.compare(current_password, user.password);
    if (!isMatch) {
      return res.render('auth/change-password', {
        title: '修改密码',
        user: req.user,
        error: '当前密码不正确',
        success: null
      });
    }
    
    // Update password
    await User.updatePassword(req.user.userid, new_password);
    
    res.render('auth/change-password', {
      title: '修改密码',
      user: req.user,
      error: null,
      success: '密码已成功修改'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.render('auth/change-password', {
      title: '修改密码',
      user: req.user,
      error: '修改密码过程中发生错误',
      success: null
    });
  }
});

// Switch role
router.post('/switch-role', verifyToken, async (req, res) => {
  try {
    const { newRole } = req.body;
    const userid = req.user.userid;

    // Verify user has access to the requested role
    const userRoles = await User.getUserRoles(userid);
    const hasRole = userRoles.some(r => r.role === newRole);

    if (!hasRole) {
      return res.status(403).json({ error: '您没有访问该角色的权限' });
    }

    // Update current role in database
    await User.updateCurrentRole(userid, newRole);

    // Create new token with updated role
    const token = jwt.sign(
      {
        userid: req.user.userid,
        job_number: req.user.job_number,
        name: req.user.name,
        role: newRole,
        availableRoles: req.user.availableRoles,
        primaryRole: req.user.primaryRole
      },
      process.env.JWT_SECRET,
      { expiresIn: '1d' }
    );

    // Set new cookie
    res.cookie('token', token, {
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 1 day
    });

    // Return redirect URL based on new role
    let redirectUrl;
    switch (newRole) {
      case 'admin':
        redirectUrl = '/admin/dashboard';
        break;
      case 'director':
        redirectUrl = '/director/dashboard';
        break;
      case 'staff':
        redirectUrl = '/staff/dashboard';
        break;
      default:
        redirectUrl = '/';
    }

    res.json({ success: true, redirectUrl });
  } catch (error) {
    console.error('Switch role error:', error);
    res.status(500).json({ error: '切换角色时发生错误' });
  }
});

// Logout
router.get('/logout', (req, res) => {
  res.clearCookie('token');
  res.redirect('/login');
});

module.exports = router;