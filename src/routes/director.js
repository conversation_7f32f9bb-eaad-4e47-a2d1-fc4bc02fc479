const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const DutyRecord = require('../models/DutyRecord');
const User = require('../models/User');
const { isDirector } = require('../middleware/auth');
const { pool } = require('../utils/database');

// 配置文件上传
const upload = multer({
  storage: multer.memoryStorage(), // 使用内存存储，避免文件系统操作
  fileFilter: function (req, file, cb) {
    // 只允许Excel文件
    const allowedExtensions = ['.xlsx', '.xls'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式 (.xlsx, .xls)'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  }
});

// Director dashboard
router.get('/dashboard', isDirector, async (req, res) => {
  try {
    // 获取查询参数，如果没有则使用当前年月
    const currentYear = parseInt(req.query.year) || new Date().getFullYear();
    const currentMonth = parseInt(req.query.month) || new Date().getMonth() + 1;

    // Get all duty records for specified year and month
    const records = await DutyRecord.getAll(currentYear, currentMonth);

    // Get all staff members
    const staff = await User.getByRole('staff');

    // Calculate detailed department statistics
    const totalDutyDays = records.length;
    const uniqueStaff = [...new Set(records.map(r => r.userid))].length;
    const avgDutyPerStaff = uniqueStaff > 0 ? (totalDutyDays / uniqueStaff).toFixed(1) : 0;

    // Calculate individual staff statistics (简化版本)
    const staffStats = staff.map(member => {
      const memberRecords = records.filter(r => r.userid === member.userid);
      const dutyCount = memberRecords.length;

      return {
        ...member,
        dutyCount
      };
    });

    // 移除不再使用的统计计算

    res.render('director/dashboard', {
      title: '主任管理面板',
      user: req.user,
      records,
      staff,
      staffStats,
      stats: {
        totalDutyDays,
        uniqueStaff,
        avgDutyPerStaff,
        totalStaff: staff.length
      },
      currentYear,
      currentMonth
    });
  } catch (error) {
    console.error('Director dashboard error:', error);
    res.status(500).send('获取数据时发生错误');
  }
});

// Get all records for specific month
router.get('/records/:year/:month', isDirector, async (req, res) => {
  try {
    const { year, month } = req.params;
    
    const records = await DutyRecord.getAll(parseInt(year), parseInt(month));
    res.json(records);
  } catch (error) {
    console.error('Get monthly records error:', error);
    res.status(500).json({ error: '获取记录时发生错误' });
  }
});



// Update any duty record
router.put('/records/:recordId', isDirector, async (req, res) => {
  try {
    const { recordId } = req.params;
    const { dutyDate, leaveDate, leaveEndDate, isHolidayDuty, remarks } = req.body;

    // 处理节假日值班和普通值班的逻辑
    let finalLeaveDate = leaveDate;
    let finalLeaveEndDate = leaveEndDate;
    let isCustomLeave = false;

    if (isHolidayDuty) {
      // 节假日值班：可以有两天调休
      if (!leaveDate) {
        // 如果没有提供调休日期，自动设置为值班日期的后一天和后两天
        const dutyDateObj = new Date(dutyDate);
        dutyDateObj.setDate(dutyDateObj.getDate() + 1);
        finalLeaveDate = dutyDateObj.toISOString().split('T')[0];

        dutyDateObj.setDate(dutyDateObj.getDate() + 1);
        finalLeaveEndDate = dutyDateObj.toISOString().split('T')[0];
        isCustomLeave = false;
      } else {
        // 如果提供了调休日期，判断是否为自动调休
        const dutyDateObj = new Date(dutyDate);
        const leaveDateObj = new Date(leaveDate);
        const nextDay = new Date(dutyDate);
        nextDay.setDate(nextDay.getDate() + 1);

        // 节假日值班的自动调休是连续两天
        const dayAfterNext = new Date(dutyDate);
        dayAfterNext.setDate(dayAfterNext.getDate() + 2);

        const isAutoLeave = leaveDateObj.getTime() === nextDay.getTime() &&
                           (!leaveEndDate || new Date(leaveEndDate).getTime() === dayAfterNext.getTime());
        isCustomLeave = !isAutoLeave;
      }
    } else {
      // 普通值班：一天调休
      if (!leaveDate) {
        // 如果没有提供调休日期，自动设置为值班日期的后一天
        const dutyDateObj = new Date(dutyDate);
        dutyDateObj.setDate(dutyDateObj.getDate() + 1);
        finalLeaveDate = dutyDateObj.toISOString().split('T')[0];
        isCustomLeave = false;
      } else {
        // 如果提供了调休日期，判断是否为值班日期的后一天
        const dutyDateObj = new Date(dutyDate);
        const leaveDateObj = new Date(leaveDate);
        const nextDay = new Date(dutyDate);
        nextDay.setDate(nextDay.getDate() + 1);

        // 如果调休日期是值班日期的后一天，则为自动调休，否则为自定义调休
        isCustomLeave = leaveDateObj.getTime() !== nextDay.getTime();
      }
      // 普通值班不应该有调休结束日期
      finalLeaveEndDate = null;
    }

    await DutyRecord.updateWithCustomLeave(recordId, dutyDate, finalLeaveDate, isCustomLeave, remarks, finalLeaveEndDate, false, isHolidayDuty);
    res.json({ success: true, message: '记录更新成功' });
  } catch (error) {
    console.error('Update record error:', error);
    res.status(500).json({ error: '更新记录时发生错误' });
  }
});

// Delete any duty record
router.delete('/records/:recordId', isDirector, async (req, res) => {
  try {
    const { recordId } = req.params;
    
    await DutyRecord.delete(recordId);
    res.json({ success: true, message: '记录删除成功' });
  } catch (error) {
    console.error('Delete record error:', error);
    res.status(500).json({ error: '删除记录时发生错误' });
  }
});

// Get detailed staff statistics
router.get('/stats/:userid', isDirector, async (req, res) => {
  try {
    const { userid } = req.params;
    const year = parseInt(req.query.year) || new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    // Get all records for the user in the specified year (包含请假记录)
    const [yearRecords] = await pool.execute(
      `SELECT * FROM duty_records WHERE userid = ? AND (
        (is_leave_record = 0 AND YEAR(duty_date) = ?) OR
        (is_leave_record = 1 AND YEAR(leave_date) = ?)
      ) ORDER BY
        CASE
          WHEN is_leave_record = 0 THEN duty_date
          ELSE leave_date
        END`,
      [userid, year, year]
    );

    // Get current month records (包含请假记录)
    const [monthRecords] = await pool.execute(
      `SELECT * FROM duty_records WHERE userid = ? AND (
        (is_leave_record = 0 AND YEAR(duty_date) = ? AND MONTH(duty_date) = ?) OR
        (is_leave_record = 1 AND YEAR(leave_date) = ? AND MONTH(leave_date) = ?)
      )`,
      [userid, year, currentMonth, year, currentMonth]
    );

    // 简化统计计算，只保留必要的数据
    const dutyRecords = yearRecords.filter(r => !r.is_leave_record);
    const leaveRecords = yearRecords.filter(r => r.is_leave_record);

    const totalDutyDays = dutyRecords.length;
    const totalLeaveDays = dutyRecords.filter(r => r.leave_date).length;
    const currentMonthDuty = monthRecords.filter(r => !r.is_leave_record).length;
    const avgDutyPerMonth = (totalDutyDays / 12).toFixed(1);

    // 简化月度统计
    const monthlyStats = {};
    for (let month = 1; month <= 12; month++) {
      const monthlyDutyRecords = dutyRecords.filter(r => r.duty_date && new Date(r.duty_date).getMonth() + 1 === month);
      monthlyStats[month] = {
        dutyDays: monthlyDutyRecords.length,
        leaveDays: monthlyDutyRecords.filter(r => r.leave_date).length
      };
    }

    const stats = {
      totalDutyDays,
      totalLeaveDays,
      currentMonthDuty,
      avgDutyPerMonth: parseFloat(avgDutyPerMonth),
      monthlyStats,
      year
    };

    res.json(stats);
  } catch (error) {
    console.error('Get detailed staff stats error:', error);
    res.status(500).json({ error: '获取统计数据时发生错误' });
  }
});

// Get department users for record creation
router.get('/users', isDirector, async (req, res) => {
  try {
    // For now, get all staff users (in a real system, this would be filtered by department)
    const users = await User.getByRole('staff');
    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: '获取用户列表时发生错误' });
  }
});

// Create duty record
router.post('/records', isDirector, async (req, res) => {
  try {
    const { userid, dutyDate, leaveDate, leaveEndDate, remarks, isLeaveRecord } = req.body;

    // 验证必填字段
    if (!userid) {
      return res.status(400).json({ error: '用户ID不能为空' });
    }

    if (isLeaveRecord) {
      // 请假记录：必须有请假开始日期，值班日期可以为空
      if (!leaveDate) {
        return res.status(400).json({ error: '请假记录必须指定请假开始日期' });
      }

      // 请假记录的处理逻辑
      const recordId = await DutyRecord.createWithCustomLeave(
        userid,
        null, // 请假记录值班日期为空
        leaveDate,
        false, // 请假记录不是自定义调休
        remarks || '请假',
        leaveEndDate,
        true // 标记为请假记录
      );
      res.json({ success: true, recordId, message: '请假记录创建成功' });
    } else {
      // 值班记录：必须有值班日期
      if (!dutyDate) {
        return res.status(400).json({ error: '值班记录必须指定值班日期' });
      }

      // 自动判断是否自定义调休
      let finalLeaveDate = leaveDate;
      let isCustomLeave = false;

      if (!leaveDate) {
        // 如果没有提供调休日期，自动设置为值班日期的后一天
        const dutyDateObj = new Date(dutyDate);
        dutyDateObj.setDate(dutyDateObj.getDate() + 1);
        finalLeaveDate = dutyDateObj.toISOString().split('T')[0];
        isCustomLeave = false;
      } else {
        // 如果提供了调休日期，判断是否为值班日期的后一天
        const dutyDateObj = new Date(dutyDate);
        const leaveDateObj = new Date(leaveDate);
        const nextDay = new Date(dutyDate);
        nextDay.setDate(nextDay.getDate() + 1);

        // 如果调休日期是值班日期的后一天，则为自动调休，否则为自定义调休
        isCustomLeave = leaveDateObj.getTime() !== nextDay.getTime();
      }

      const recordId = await DutyRecord.createWithCustomLeave(userid, dutyDate, finalLeaveDate, isCustomLeave, remarks, null, false, false);
      res.json({ success: true, recordId, message: '值班记录创建成功' });
    }
  } catch (error) {
    console.error('Create duty record error:', error);
    res.status(500).json({ error: '创建记录时发生错误' });
  }
});

// Download duty import template
router.get('/duty-template', isDirector, async (req, res) => {
  try {
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('值班安排导入模板');

    // 设置列标题
    worksheet.columns = [
      { header: '工号', key: 'job_number', width: 15 },
      { header: '姓名', key: 'name', width: 15 },
      { header: '值班日期', key: 'duty_date', width: 15 },
      { header: '调休日期', key: 'leave_date', width: 15 }
    ];

    // 添加示例数据
    worksheet.addRow({
      job_number: '001',
      name: '张三',
      duty_date: '2024-06-15',
      leave_date: '2024-06-16'
    });

    worksheet.addRow({
      job_number: '002',
      name: '李四',
      duty_date: '2024-06-16',
      leave_date: '' // 空值示例
    });

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=duty_import_template.xlsx');

    // 发送文件
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('Download duty template error:', error);
    res.status(500).json({ error: '下载模板时发生错误' });
  }
});

// Bulk import duty records
router.post('/duty-import', isDirector, upload.single('dutyFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请选择要导入的文件' });
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      return res.status(400).json({ error: '无法读取Excel文件' });
    }

    const results = {
      successCount: 0,
      errorCount: 0,
      errors: []
    };

    // 跳过标题行，从第2行开始处理
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // 跳过空行
      if (!row.getCell(1).value) continue;

      try {
        const jobNumber = row.getCell(1).value ? row.getCell(1).value.toString().trim() : '';
        const name = row.getCell(2).value ? row.getCell(2).value.toString().trim() : '';
        const dutyDate = row.getCell(3).value;
        const leaveDate = row.getCell(4).value;

        // 验证必填字段
        if (!jobNumber || !dutyDate) {
          results.errors.push(`第${rowNumber}行：工号和值班日期不能为空`);
          results.errorCount++;
          continue;
        }

        // 查找用户
        const user = await User.getByJobNumber(jobNumber);
        if (!user) {
          results.errors.push(`第${rowNumber}行：工号 ${jobNumber} 不存在`);
          results.errorCount++;
          continue;
        }

        // 处理日期
        let formattedDutyDate;
        let formattedLeaveDate = null;

        if (dutyDate instanceof Date) {
          formattedDutyDate = dutyDate.toISOString().split('T')[0];
        } else {
          formattedDutyDate = dutyDate.toString();
        }

        if (leaveDate) {
          if (leaveDate instanceof Date) {
            formattedLeaveDate = leaveDate.toISOString().split('T')[0];
          } else {
            formattedLeaveDate = leaveDate.toString();
          }
        }

        // 自动判断是否自定义调休
        let finalLeaveDate = formattedLeaveDate;
        let isCustomLeave = false;

        if (!formattedLeaveDate) {
          // 如果没有提供调休日期，自动设置为值班日期的后一天
          const dutyDateObj = new Date(formattedDutyDate);
          dutyDateObj.setDate(dutyDateObj.getDate() + 1);
          finalLeaveDate = dutyDateObj.toISOString().split('T')[0];
          isCustomLeave = false;
        } else {
          // 如果提供了调休日期，判断是否为值班日期的后一天
          const dutyDateObj = new Date(formattedDutyDate);
          const leaveDateObj = new Date(formattedLeaveDate);
          const nextDay = new Date(formattedDutyDate);
          nextDay.setDate(nextDay.getDate() + 1);

          // 如果调休日期是值班日期的后一天，则为自动调休，否则为自定义调休
          isCustomLeave = leaveDateObj.getTime() !== nextDay.getTime();
        }

        // 创建记录（批量导入不包含备注，不支持请假记录和节假日值班）
        await DutyRecord.createWithCustomLeave(user.userid, formattedDutyDate, finalLeaveDate, isCustomLeave, '', null, false, false);
        results.successCount++;

      } catch (error) {
        console.error(`Error processing row ${rowNumber}:`, error);
        results.errors.push(`第${rowNumber}行：${error.message}`);
        results.errorCount++;
      }
    }

    // 清理上传的文件
    if (req.file && req.file.path) {
      const fs = require('fs');
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error deleting file:', err);
      });
    }

    if (results.successCount > 0) {
      res.json({
        success: true,
        message: `成功导入 ${results.successCount} 条记录`,
        details: results
      });
    } else {
      res.json({
        success: false,
        error: '没有成功导入任何记录',
        details: results
      });
    }

  } catch (error) {
    console.error('Duty import error:', error);
    res.status(500).json({ error: '导入过程中发生错误' });
  }
});

module.exports = router;
