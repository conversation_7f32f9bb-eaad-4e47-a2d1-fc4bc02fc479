/**
 * 密码强度验证工具
 */

/**
 * 验证密码强度
 * @param {string} password - 要验证的密码
 * @returns {object} 验证结果
 */
function validatePasswordStrength(password) {
  const result = {
    isValid: false,
    errors: []
  };

  // 检查长度
  if (!password || password.length < 8) {
    result.errors.push('密码长度至少8位');
  }

  // 检查是否包含大写字母
  if (!/[A-Z]/.test(password)) {
    result.errors.push('密码必须包含大写字母');
  }

  // 检查是否包含小写字母
  if (!/[a-z]/.test(password)) {
    result.errors.push('密码必须包含小写字母');
  }

  // 检查是否包含数字
  if (!/[0-9]/.test(password)) {
    result.errors.push('密码必须包含数字');
  }

  // 如果没有错误，则密码有效
  result.isValid = result.errors.length === 0;

  return result;
}

/**
 * 获取密码强度描述
 * @param {string} password - 要检查的密码
 * @returns {string} 强度描述
 */
function getPasswordStrengthDescription(password) {
  const validation = validatePasswordStrength(password);
  
  if (validation.isValid) {
    return '密码强度符合要求';
  } else {
    return `密码强度不足：${validation.errors.join('，')}`;
  }
}

module.exports = {
  validatePasswordStrength,
  getPasswordStrengthDescription
};
