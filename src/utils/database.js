const mysql = require('mysql2/promise');
require('dotenv').config();

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Database initialization function
async function initDatabase() {
  try {
    const connection = await pool.getConnection();
    
    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        userid INT AUTO_INCREMENT PRIMARY KEY,
        job_number VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('staff', 'director', 'admin') NOT NULL
      )
    `);
    
    // Create duty_records table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS duty_records (
        record_id INT AUTO_INCREMENT PRIMARY KEY,
        userid INT NOT NULL,
        duty_date DATE NOT NULL,
        leave_date DATE,
        is_custom_leave BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (userid) REFERENCES users(userid),
        INDEX idx_userid_duty_date (userid, duty_date)
      )
    `);
    
    // Check if admin user exists, create if not
    const [rows] = await connection.execute('SELECT * FROM users WHERE job_number = ?', ['admin']);
    if (rows.length === 0) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin', 10);

      // Create admin user with admin as primary role
      const [result] = await connection.execute(
        'INSERT INTO users (job_number, name, password, role) VALUES (?, ?, ?, ?)',
        ['admin', '系统管理员', hashedPassword, 'admin']
      );

      const adminUserId = result.insertId;

      // Add admin role as primary
      await connection.execute(
        'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
        [adminUserId, 'admin', true]
      );

      // Add director role as secondary
      await connection.execute(
        'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
        [adminUserId, 'director', false]
      );

      console.log('Admin user created with admin and director roles');
    } else {
      // Check if existing admin user has multiple roles
      const adminUser = rows[0];
      const [roleRows] = await connection.execute(
        'SELECT * FROM user_roles WHERE userid = ?',
        [adminUser.userid]
      );

      if (roleRows.length === 0) {
        // Migrate existing admin user to multi-role system
        console.log('Migrating existing admin user to multi-role system...');

        // Add admin role as primary
        await connection.execute(
          'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
          [adminUser.userid, 'admin', true]
        );

        // Add director role as secondary
        await connection.execute(
          'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
          [adminUser.userid, 'director', false]
        );

        console.log('Admin user migrated to multi-role system');
      } else if (roleRows.length === 1 && roleRows[0].role === 'admin') {
        // Admin user has only admin role, add director role
        const hasDirectorRole = roleRows.some(r => r.role === 'director');
        if (!hasDirectorRole) {
          await connection.execute(
            'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
            [adminUser.userid, 'director', false]
          );
          console.log('Added director role to existing admin user');
        }
      }
    }
    
    connection.release();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Database initialization error:', error);
  }
}

module.exports = { pool, initDatabase };