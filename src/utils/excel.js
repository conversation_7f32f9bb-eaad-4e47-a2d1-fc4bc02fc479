const ExcelJS = require('exceljs');
const User = require('../models/User');
const DutyRecord = require('../models/DutyRecord');

// Import duty records from Excel file
async function importDutyRecords(filePath) {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(filePath);
  const worksheet = workbook.getWorksheet(1);
  
  const results = {
    success: 0,
    errors: []
  };
  
  // Start from row 2 (assuming row 1 is header)
  for (let i = 2; i <= worksheet.rowCount; i++) {
    const row = worksheet.getRow(i);
    const name = row.getCell(1).value;
    const dutyDateCell = row.getCell(2).value;
    
    if (!name || !dutyDateCell) continue;
    
    try {
      // Convert Excel date to YYYY-MM-DD format
      let dutyDate;
      if (dutyDateCell instanceof Date) {
        dutyDate = dutyDateCell.toISOString().split('T')[0];
      } else {
        dutyDate = new Date(dutyDateCell).toISOString().split('T')[0];
      }
      
      // Find user by name
      const [user] = await pool.execute('SELECT userid FROM users WHERE name = ?', [name]);
      
      if (user.length === 0) {
        results.errors.push(`Row ${i}: User "${name}" not found`);
        continue;
      }
      
      // Create duty record
      await DutyRecord.create(user[0].userid, dutyDate);
      results.success++;
    } catch (error) {
      results.errors.push(`Row ${i}: ${error.message}`);
    }
  }
  
  return results;
}

// Import users from Excel file
async function importUsers(filePath) {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(filePath);
  const worksheet = workbook.getWorksheet(1);
  
  const results = {
    success: 0,
    errors: []
  };
  
  // Start from row 2 (assuming row 1 is header)
  for (let i = 2; i <= worksheet.rowCount; i++) {
    const row = worksheet.getRow(i);
    const jobNumber = row.getCell(1).value;
    const name = row.getCell(2).value;
    const password = row.getCell(3).value;
    const role = row.getCell(4).value;
    
    if (!jobNumber || !name || !password || !role) continue;
    
    try {
      // Validate role
      if (!['staff', 'director', 'admin'].includes(role)) {
        results.errors.push(`Row ${i}: Invalid role "${role}"`);
        continue;
      }
      
      // Check if job number already exists
      const existingUser = await User.findByJobNumber(jobNumber);
      if (existingUser) {
        results.errors.push(`Row ${i}: Job number "${jobNumber}" already exists`);
        continue;
      }
      
      // Create user
      await User.createUser(jobNumber, name, password, role);
      results.success++;
    } catch (error) {
      results.errors.push(`Row ${i}: ${error.message}`);
    }
  }
  
  return results;
}

// Export duty records to Excel
async function exportDutyRecords(records) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Duty Records');
  
  worksheet.columns = [
    { header: '姓名', key: 'name', width: 15 },
    { header: '工号', key: 'job_number', width: 15 },
    { header: '值班日期', key: 'duty_date', width: 15 },
    { header: '调休日期', key: 'leave_date', width: 15 }
  ];
  
  records.forEach(record => {
    worksheet.addRow({
      name: record.name,
      job_number: record.job_number,
      duty_date: new Date(record.duty_date).toLocaleDateString(),
      leave_date: record.leave_date ? new Date(record.leave_date).toLocaleDateString() : ''
    });
  });
  
  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
}

module.exports = { importDutyRecords, importUsers, exportDutyRecords };