const { pool } = require('../utils/database');
const bcrypt = require('bcryptjs');

class User {
  static async findByJobNumber(jobNumber) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM users WHERE job_number = ?',
        [jobNumber]
      );
      return rows[0];
    } catch (error) {
      console.error('Error finding user:', error);
      throw error;
    }
  }

  static async findById(userid) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM users WHERE userid = ?',
        [userid]
      );
      return rows[0];
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw error;
    }
  }

  static async getAllUsers() {
    try {
      const [rows] = await pool.execute(
        'SELECT userid, job_number, name, role FROM users ORDER BY userid'
      );
      return rows;
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  static async createUser(jobNumber, name, password, role) {
    try {
      const hashedPassword = await bcrypt.hash(password, 10);
      const [result] = await pool.execute(
        'INSERT INTO users (job_number, name, password, role) VALUES (?, ?, ?, ?)',
        [jobNumber, name, hashedPassword, role]
      );
      return result.insertId;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  static async updatePassword(userid, newPassword) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      await pool.execute(
        'UPDATE users SET password = ? WHERE userid = ?',
        [hashedPassword, userid]
      );
      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  }

  static async updateRole(userid, newRole) {
    try {
      await pool.execute(
        'UPDATE users SET role = ? WHERE userid = ?',
        [newRole, userid]
      );
      return true;
    } catch (error) {
      console.error('Error updating role:', error);
      throw error;
    }
  }

  // Alias methods for consistency with route files
  static async getByJobNumber(jobNumber) {
    return this.findByJobNumber(jobNumber);
  }

  static async getById(userid) {
    return this.findById(userid);
  }

  static async getAll() {
    return this.getAllUsers();
  }

  static async getByRole(role) {
    try {
      const [rows] = await pool.execute(
        'SELECT userid, job_number, name, role FROM users WHERE role = ? ORDER BY userid',
        [role]
      );
      return rows;
    } catch (error) {
      console.error('Error getting users by role:', error);
      throw error;
    }
  }

  static async create(name, jobNumber, role, password) {
    return this.createUser(jobNumber, name, password, role);
  }

  static async update(userid, name, jobNumber, role) {
    try {
      await pool.execute(
        'UPDATE users SET name = ?, job_number = ?, role = ? WHERE userid = ?',
        [name, jobNumber, role, userid]
      );
      return true;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  static async delete(userid) {
    try {
      await pool.execute('DELETE FROM users WHERE userid = ?', [userid]);
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // 多角色支持方法
  static async getUserRoles(userid) {
    try {
      const [rows] = await pool.execute(
        'SELECT role, is_primary FROM user_roles WHERE userid = ? ORDER BY is_primary DESC, role',
        [userid]
      );
      return rows;
    } catch (error) {
      console.error('Error getting user roles:', error);
      throw error;
    }
  }

  static async addUserRole(userid, role, isPrimary = false) {
    try {
      await pool.execute(
        'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE is_primary = VALUES(is_primary)',
        [userid, role, isPrimary]
      );
      return true;
    } catch (error) {
      console.error('Error adding user role:', error);
      throw error;
    }
  }

  static async removeUserRole(userid, role) {
    try {
      await pool.execute(
        'DELETE FROM user_roles WHERE userid = ? AND role = ?',
        [userid, role]
      );
      return true;
    } catch (error) {
      console.error('Error removing user role:', error);
      throw error;
    }
  }

  static async updateCurrentRole(userid, currentRole) {
    try {
      await pool.execute(
        'UPDATE users SET current_role = ? WHERE userid = ?',
        [currentRole, userid]
      );
      return true;
    } catch (error) {
      console.error('Error updating current role:', error);
      throw error;
    }
  }

  static async findByJobNumberWithRoles(jobNumber) {
    try {
      const [userRows] = await pool.execute(
        'SELECT * FROM users WHERE job_number = ?',
        [jobNumber]
      );

      if (userRows.length === 0) {
        return null;
      }

      const user = userRows[0];
      const roles = await this.getUserRoles(user.userid);

      const primaryRole = roles.find(r => r.is_primary);
      const firstRole = roles[0];

      return {
        ...user,
        roles: roles,
        availableRoles: roles.map(r => r.role),
        primaryRole: primaryRole ? primaryRole.role : (firstRole ? firstRole.role : null)
      };
    } catch (error) {
      console.error('Error finding user with roles:', error);
      throw error;
    }
  }
}

module.exports = User;