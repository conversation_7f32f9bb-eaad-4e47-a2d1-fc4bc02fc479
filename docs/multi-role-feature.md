# 多角色身份切换功能

## 概述

值班管理系统现在支持一个账号拥有多个权限身份，用户登录后可以在右上角的导航栏中切换不同身份的页面，无需重新登录即可访问不同角色的功能。

## 功能特性

### 🎭 多角色支持

#### **角色类型**
- **员工 (staff)**: 查看个人值班记录和统计
- **主任 (director)**: 管理部门值班记录和员工
- **管理员 (admin)**: 系统全局管理权限

#### **多角色组合**
- 用户可以同时拥有多个角色
- 每个用户有一个主要角色（登录时的默认角色）
- 可以动态切换到其他拥有的角色

### 🔄 身份切换

#### **切换方式**
1. **导航栏下拉菜单**: 右上角显示当前角色，点击可展开其他可用角色
2. **一键切换**: 点击目标角色即可切换，页面自动跳转
3. **权限验证**: 只能切换到用户拥有的角色

#### **界面显示**
- 当前角色显示在导航栏右上角
- 多角色用户显示下拉箭头图标
- 不同角色使用不同图标标识

### 🔐 权限管理

#### **角色权限继承**
- 拥有高级角色不会自动获得低级角色权限
- 每个角色都需要明确授予
- 权限检查基于当前激活的角色

#### **数据访问控制**
- **员工角色**: 只能访问个人数据
- **主任角色**: 可以访问部门数据
- **管理员角色**: 可以访问全部数据

## 技术实现

### 🗄️ 数据库设计

#### **用户角色关联表 (user_roles)**
```sql
CREATE TABLE user_roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userid INT NOT NULL,
  role ENUM('staff', 'director', 'admin') NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userid) REFERENCES users(userid) ON DELETE CASCADE,
  UNIQUE KEY unique_user_role (userid, role)
);
```

#### **用户表扩展**
```sql
ALTER TABLE users ADD COLUMN current_role ENUM('staff', 'director', 'admin') NULL;
```

### 🔧 后端实现

#### **用户模型扩展**
```javascript
// 获取用户所有角色
static async getUserRoles(userid)

// 添加用户角色
static async addUserRole(userid, role, isPrimary)

// 移除用户角色
static async removeUserRole(userid, role)

// 更新当前角色
static async updateCurrentRole(userid, currentRole)

// 获取用户及其角色信息
static async findByJobNumberWithRoles(jobNumber)
```

#### **认证逻辑更新**
```javascript
// JWT Token 包含多角色信息
{
  userid: user.userid,
  job_number: user.job_number,
  name: user.name,
  role: currentRole,              // 当前激活角色
  availableRoles: user.availableRoles,  // 可用角色列表
  primaryRole: user.primaryRole   // 主要角色
}
```

#### **角色切换API**
```javascript
POST /switch-role
{
  "newRole": "director"
}
```

### 🎨 前端实现

#### **导航栏下拉菜单**
```html
<div class="navbar-item has-dropdown is-hoverable">
  <a class="navbar-link">
    <i class="fas fa-user-tag"></i>
    <span>当前角色</span>
    <i class="fas fa-angle-down"></i>
  </a>
  <div class="navbar-dropdown is-right">
    <!-- 其他可用角色 -->
  </div>
</div>
```

#### **角色切换JavaScript**
```javascript
function switchRole(newRole) {
  fetch('/switch-role', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ newRole: newRole })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      window.location.href = data.redirectUrl;
    }
  });
}
```

## 使用场景

### 👥 典型用户场景

#### **场景1: 兼职主任**
- **用户**: 李老师
- **角色**: 员工 + 主任
- **使用**: 平时以员工身份查看个人记录，需要时切换到主任身份管理部门

#### **场景2: 技术管理员**
- **用户**: 王老师
- **角色**: 员工 + 管理员
- **使用**: 平时以员工身份工作，需要时切换到管理员身份进行系统管理

#### **场景3: 部门负责人**
- **用户**: 张主任
- **角色**: 员工 + 主任 + 管理员
- **使用**: 根据工作需要在三个角色间灵活切换

### 🎯 业务价值

#### **提高效率**
- 无需多个账号，一个账号多种身份
- 快速切换角色，无需重新登录
- 减少账号管理复杂度

#### **权限精确**
- 基于当前角色的精确权限控制
- 避免权限过度授予的安全风险
- 清晰的角色职责划分

#### **用户体验**
- 直观的角色显示和切换界面
- 平滑的角色转换体验
- 一致的界面设计风格

## 安全考虑

### 🛡️ 权限验证

#### **多层验证**
1. **前端验证**: 界面元素根据角色显示/隐藏
2. **路由验证**: 中间件检查用户是否拥有当前角色
3. **API验证**: 每个API调用都验证当前角色权限

#### **会话管理**
- JWT Token 包含完整的角色信息
- 角色切换时更新Token和数据库
- Token过期时需要重新登录

### 🔒 数据安全

#### **角色隔离**
- 不同角色访问不同的数据范围
- 严格的数据访问控制
- 防止角色权限泄露

#### **审计日志**
- 记录角色切换操作
- 跟踪用户行为轨迹
- 便于安全审计

## 管理功能

### 👨‍💼 管理员功能

#### **角色分配**
- 为用户添加/移除角色
- 设置用户的主要角色
- 批量角色管理

#### **权限监控**
- 查看用户角色分配情况
- 监控角色使用统计
- 权限异常检测

### 📊 统计报告

#### **角色使用统计**
- 多角色用户数量
- 角色切换频率
- 角色使用时长分布

#### **权限分析**
- 角色权限覆盖分析
- 用户权限使用情况
- 权限优化建议

## 示例数据

### 📋 当前系统中的多角色用户

| 用户名 | 工号 | 主要角色 | 额外角色 | 说明 |
|--------|------|----------|----------|------|
| 李老师 | S001 | 员工 | 主任 | 兼职主任，可管理部门 |
| 王老师 | S002 | 员工 | 管理员 | 技术管理员，可管理系统 |
| 张主任 | D001 | 主任 | - | 专职主任 |
| 系统管理员 | admin | 管理员 | - | 专职管理员 |
| 赵老师 | S003 | 员工 | - | 普通员工 |

### 🔄 角色切换流程

1. **登录**: 使用主要角色登录系统
2. **查看**: 在导航栏看到当前角色和下拉箭头
3. **选择**: 点击下拉菜单选择其他角色
4. **确认**: 确认切换操作
5. **跳转**: 自动跳转到新角色对应的页面

## 总结

多角色身份切换功能为值班管理系统带来了更大的灵活性和更好的用户体验。用户可以根据实际工作需要在不同角色间切换，既保证了权限的精确控制，又提高了工作效率。

这个功能特别适合以下场景：
- 兼职管理人员
- 技术支持人员
- 多部门协调人员
- 临时权限需求

通过合理的角色分配和权限管理，可以让系统更好地适应复杂的组织结构和工作流程。
