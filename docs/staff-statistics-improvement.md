# 员工统计功能优化

## 概述

本次优化主要解决了普通用户界面中"剩余调休"计算不准确的问题，将请假天数从调休计算中分离出来，并在界面上单独显示请假信息。

## 问题分析

### 🔍 原有问题

1. **统计混淆**: 请假记录被错误地计入值班和调休统计
2. **计算错误**: 剩余调休 = 值班天数 - 调休天数，但包含了请假记录
3. **信息缺失**: 用户无法清楚看到自己的请假天数

### 🎯 优化目标

1. **分离统计**: 值班记录和请假记录分开计算
2. **准确计算**: 剩余调休只基于实际值班记录
3. **清晰显示**: 请假天数单独显示

## 技术实现

### 🔧 后端优化

#### **DutyRecord.getStats() 方法重构**

**修改前**:
```javascript
// 混合计算值班和请假记录
const [dutyRows] = await pool.execute(
  'SELECT COUNT(*) as dutyCount FROM duty_records WHERE userid = ? AND YEAR(duty_date) = ?',
  [userid, yearToUse]
);
```

**修改后**:
```javascript
// 只计算值班记录，排除请假记录
const [dutyRows] = await pool.execute(
  'SELECT COUNT(*) as dutyCount FROM duty_records WHERE userid = ? AND is_leave_record = 0 AND YEAR(duty_date) = ?',
  [userid, yearToUse]
);

// 单独计算请假记录
const [leaveRecordRows] = await pool.execute(
  'SELECT COUNT(*) as leaveRecordCount FROM duty_records WHERE userid = ? AND is_leave_record = 1 AND YEAR(leave_date) = ?',
  [userid, yearToUse]
);
```

#### **新增统计字段**

```javascript
return {
  yearDutyCount: dutyRows[0].dutyCount,           // 值班天数（不含请假）
  yearLeaveCount: leaveRows[0].leaveCount,        // 调休天数（不含请假）
  monthDutyCount: monthDutyRows[0].monthDutyCount, // 本月值班
  remainingLeaveCount: dutyRows[0].dutyCount - leaveRows[0].leaveCount, // 剩余调休
  leaveRecordCount: leaveRecordRows[0].leaveRecordCount, // 请假记录数
  totalLeaveDays: totalLeaveDays                  // 请假总天数
};
```

#### **请假天数计算**

支持单天和多天请假：
```sql
SELECT 
  SUM(
    CASE 
      WHEN leave_end_date IS NOT NULL THEN 
        DATEDIFF(leave_end_date, leave_date) + 1
      ELSE 1
    END
  ) as totalLeaveDays
FROM duty_records 
WHERE userid = ? AND is_leave_record = 1 AND YEAR(leave_date) = ?
```

### 🎨 前端优化

#### **统计卡片保持不变**

```html
<!-- 四个统计卡片 -->
<div class="column is-3">
    <div class="card has-text-centered">
        <div class="card-content">
            <h5 class="title is-5">本年值班</h5>
            <h3 class="title is-3 has-text-primary"><%= stats.yearDutyCount %></h3>
            <p class="subtitle is-6">天</p>
        </div>
    </div>
</div>
<!-- 本年调休、本月值班、剩余调休 类似 -->
```

#### **新增请假天数显示**

```html
<!-- 条件显示请假信息 -->
<% if (stats.totalLeaveDays > 0) { %>
<div class="columns mb-4">
    <div class="column">
        <div class="notification is-info is-light">
            <div class="level">
                <div class="level-left">
                    <div class="level-item">
                        <span class="icon">
                            <i class="fas fa-calendar-times"></i>
                        </span>
                        <span><strong>请假 <%= stats.totalLeaveDays %> 天</strong></span>
                    </div>
                </div>
                <div class="level-right">
                    <div class="level-item">
                        <small class="has-text-grey">本年度累计请假天数</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<% } %>
```

## 功能特性

### ✅ 统计准确性

**值班统计**:
- ✅ 只计算 `is_leave_record = 0` 的记录
- ✅ 排除请假记录对值班统计的影响

**调休统计**:
- ✅ 只计算值班记录的调休天数
- ✅ 请假记录的调休不计入统计

**剩余调休**:
- ✅ 公式：值班天数 - 已安排调休天数
- ✅ 不受请假记录影响

### ✅ 请假信息显示

**条件显示**:
- ✅ 只有存在请假天数时才显示
- ✅ 避免界面冗余信息

**天数计算**:
- ✅ 单天请假：计为1天
- ✅ 多天请假：结束日期 - 开始日期 + 1

**界面设计**:
- ✅ 使用蓝色信息提示框
- ✅ 图标和文字清晰易懂

### ✅ 表格显示优化

**请假记录标识**:
- ✅ 值班日期列显示"请假"标签
- ✅ 调休日期列显示请假日期范围

**日期格式**:
- ✅ 统一使用 YYYY-MM-DD 格式
- ✅ 多天请假显示为"开始日期 至 结束日期"

## 测试验证

### 🧪 测试用例

**测试用户**: 李老师 (S001)

**2024年数据**:
- 值班记录：3条
- 调休记录：3条
- 剩余调休：0天
- 请假记录：0条
- 请假天数：0天

**2025年数据**:
- 值班记录：0条
- 调休记录：0条
- 剩余调休：0天
- 请假记录：1条
- 请假天数：1天

### ✅ 验证结果

**统计计算**:
- ✅ 值班和请假记录正确分离
- ✅ 剩余调休计算准确
- ✅ 请假天数计算正确

**界面显示**:
- ✅ 统计卡片显示正确
- ✅ 请假提示条件显示
- ✅ 表格记录类型清晰

## 用户体验提升

### 🎯 清晰的信息架构

**统计卡片区域**:
1. 本年值班 - 实际值班天数
2. 本年调休 - 已安排的调休天数
3. 本月值班 - 当月值班天数
4. 剩余调休 - 还可以调休的天数

**请假信息区域**:
- 只在有请假时显示
- 清楚标明请假总天数
- 区别于调休信息

**记录表格区域**:
- 值班记录和请假记录混合显示
- 通过标签区分记录类型
- 支持年月筛选

### 🎯 逻辑清晰

**调休逻辑**:
```
剩余调休 = 值班天数 - 已安排调休天数
```

**请假逻辑**:
```
请假天数 = 所有请假记录的天数总和
```

**显示逻辑**:
- 统计卡片：始终显示
- 请假提示：有请假时显示
- 记录表格：所有记录混合显示

## 兼容性说明

### 📱 数据兼容

- ✅ 现有数据结构不变
- ✅ 新增字段向后兼容
- ✅ 统计逻辑向前兼容

### 📱 界面兼容

- ✅ 保持原有布局结构
- ✅ 新增元素不影响现有功能
- ✅ 响应式设计保持一致

## 总结

本次优化成功解决了员工统计中请假和调休混淆的问题，提供了更准确、更清晰的统计信息显示。用户现在可以：

1. **准确了解调休情况** - 剩余调休不受请假影响
2. **清楚看到请假信息** - 请假天数单独显示
3. **方便查看记录详情** - 表格中清晰区分记录类型

这次优化提升了系统的准确性和用户体验，使值班管理更加科学合理。
