# 用户权限管理说明

## 权限概述

系统采用基于角色的权限控制（RBAC），不同角色具有不同的功能权限和数据访问权限。

## 角色权限详细说明

### 👤 员工 (Staff)

#### **查看权限**
- ✅ **个人值班记录**: 只能查看自己的值班记录
- ✅ **个人统计**: 查看个人的值班和调休统计
- ✅ **年月筛选**: 可以按年份和月份筛选查看记录

#### **操作权限**
- ❌ **编辑记录**: 无权编辑值班记录
- ❌ **删除记录**: 无权删除值班记录
- ❌ **创建记录**: 无权创建新的值班记录
- ✅ **修改密码**: 可以修改自己的登录密码

#### **界面显示**
```
员工仪表板表格结构：
┌─────────────┬─────────────┐
│   值班日期   │   调休日期   │
├─────────────┼─────────────┤
│ 2024-06-25  │ 2024-06-26  │
│ 2024-06-15  │ 2024-06-16  │
└─────────────┴─────────────┘
```

#### **设计理念**
- **简洁界面**: 只显示必要信息，避免技术细节
- **只读模式**: 防止误操作，确保数据安全
- **联系机制**: 需要修改时联系主任或管理员

---

### 👨‍💼 主任 (Director)

#### **查看权限**
- ✅ **部门记录**: 查看部门内所有员工的值班记录
- ✅ **员工统计**: 查看部门员工的统计信息
- ✅ **详细信息**: 查看"是否自定义调休"等技术细节

#### **操作权限**
- ✅ **编辑记录**: 可以编辑部门内任何值班记录
- ✅ **删除记录**: 可以删除部门内的值班记录
- ✅ **创建记录**: 可以为部门员工创建值班记录
- ✅ **管理调休**: 可以设置自定义调休或自动调休

#### **界面显示**
```
主任仪表板表格结构：
┌──────┬──────┬───────────┬───────────┬──────────────┬──────┐
│ 工号 │ 姓名 │ 值班日期  │ 调休日期  │是否自定义调休│ 操作 │
├──────┼──────┼───────────┼───────────┼──────────────┼──────┤
│ S001 │ 李员工│2024-06-25 │2024-06-26 │      是      │[编辑]│
│ S002 │ 王员工│2024-06-15 │2024-06-16 │      否      │[删除]│
└──────┴──────┴───────────┴───────────┴──────────────┴──────┘
```

#### **管理范围**
- **部门级权限**: 只能管理本部门员工
- **完整功能**: 拥有值班记录的完整管理权限
- **业务决策**: 可以根据业务需要调整值班安排

---

### 👨‍💻 管理员 (Admin)

#### **查看权限**
- ✅ **全系统记录**: 查看所有用户的值班记录
- ✅ **用户管理**: 查看和管理所有用户信息
- ✅ **系统统计**: 查看全系统的统计数据

#### **操作权限**
- ✅ **完整记录管理**: 创建、编辑、删除任何值班记录
- ✅ **用户管理**: 创建、编辑、删除用户账户
- ✅ **批量操作**: 批量导入用户、导出数据
- ✅ **密码重置**: 重置任何用户的密码

#### **界面显示**
```
管理员仪表板表格结构：
┌──────┬───────────┬───────────┬──────────────┬──────┐
│ 姓名 │ 值班日期  │ 调休日期  │是否自定义调休│ 操作 │
├──────┼───────────┼───────────┼──────────────┼──────┤
│李老师 │2024-06-25 │2024-06-26 │      是      │[编辑]│
│王老师 │2024-06-15 │2024-06-16 │      否      │[删除]│
└──────┴───────────┴───────────┴──────────────┴──────┘
```

#### **系统级权限**
- **最高权限**: 拥有系统的最高管理权限
- **数据安全**: 负责系统数据的安全和完整性
- **用户支持**: 协助解决用户的各种问题

## 权限控制实现

### 🔒 前端权限控制

#### **界面隐藏**
```javascript
// 老师页面移除操作列
if (userRole === 'staff') {
    // 不显示"是否自定义调休"和"操作"列
    hideColumns(['is_custom_leave', 'actions']);
}
```

#### **功能限制**
```javascript
// 编辑功能权限检查
function editRecord(recordId) {
    if (currentPath.includes('/staff/')) {
        alert('员工没有编辑权限，如需修改请联系主任或管理员');
        return;
    }
    // 继续编辑流程...
}
```

### 🛡️ 后端权限验证

#### **路由保护**
```javascript
// 员工路由 - 只能访问自己的数据
router.get('/records', isStaff, async (req, res) => {
    const records = await DutyRecord.getByUserId(req.user.userid);
    // 只返回当前用户的记录
});

// 主任路由 - 可以访问部门数据
router.get('/records', isDirector, async (req, res) => {
    const records = await DutyRecord.getByDepartment(req.user.department);
    // 返回部门内所有记录
});
```

#### **数据过滤**
- **员工**: 查询条件自动添加 `WHERE userid = ?`
- **主任**: 查询条件添加部门限制
- **管理员**: 无查询限制，可访问全部数据

## 安全考虑

### 🔐 数据安全

1. **最小权限原则**: 每个角色只获得完成工作所需的最小权限
2. **数据隔离**: 员工无法看到其他人的记录
3. **操作审计**: 记录所有的数据修改操作
4. **权限验证**: 前后端双重权限验证

### 🚫 防护措施

1. **界面隐藏**: 员工看不到编辑和删除按钮
2. **功能禁用**: 即使调用编辑函数也会被拒绝
3. **API保护**: 后端API验证用户权限
4. **错误提示**: 友好的权限不足提示

## 用户体验

### 💡 设计原则

1. **角色适配**: 界面根据用户角色自动调整
2. **信息透明**: 清楚告知用户权限范围
3. **操作引导**: 提供明确的操作指导
4. **错误友好**: 权限不足时给出友好提示

### 📱 界面优化

- **员工界面**: 简洁清爽，专注于信息查看
- **管理界面**: 功能完整，支持复杂操作
- **响应式设计**: 在不同设备上都有良好体验
- **一致性**: 保持整体设计风格统一

## 权限升级流程

### 📈 权限申请

1. **员工 → 主任**: 通过组织流程申请
2. **主任 → 管理员**: 系统管理员审批
3. **临时权限**: 特殊情况下的临时授权
4. **权限回收**: 定期审查和权限回收

### 🔄 权限变更

- **即时生效**: 权限变更后立即生效
- **会话更新**: 用户需要重新登录获取新权限
- **通知机制**: 权限变更时通知相关用户
- **记录保存**: 保存权限变更的历史记录

通过这种分层的权限管理，系统确保了数据安全，同时为不同角色提供了适合的用户体验。
