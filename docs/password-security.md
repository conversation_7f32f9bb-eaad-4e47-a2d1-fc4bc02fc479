# 密码安全策略

## 概述

为了提高系统安全性，值班管理系统现在实施了严格的密码强度要求。所有用户在修改密码、创建新用户或重置密码时都必须遵循这些要求。

## 密码强度要求

### ✅ 必须满足的条件

1. **长度要求**: 至少8位字符
2. **大写字母**: 必须包含至少一个大写字母 (A-Z)
3. **小写字母**: 必须包含至少一个小写字母 (a-z)
4. **数字**: 必须包含至少一个数字 (0-9)

### ✅ 有效密码示例

- `Password123`
- `MySecure1`
- `Admin2024`
- `StrongPass9`
- `NewPassword1`

### ❌ 无效密码示例

- `123456` - 长度不足，缺少字母
- `password` - 缺少大写字母和数字
- `Password` - 缺少数字
- `PASSWORD123` - 缺少小写字母
- `MyPass1` - 长度不足

## 功能覆盖范围

### 🔐 修改密码

**位置**: `/change-password`

**功能**:
- 实时密码强度检测
- 可视化密码要求检查列表
- 密码匹配验证
- 前端和后端双重验证

**界面特性**:
- 密码输入时实时显示强度状态
- 绿色✓表示满足要求，灰色•表示未满足
- 只有密码强度符合要求且两次输入匹配时才能提交

### 👥 管理员创建用户

**位置**: 管理员用户管理页面

**功能**:
- 创建用户时的密码强度验证
- 实时密码强度提示
- 创建按钮根据密码强度启用/禁用

**界面特性**:
- 密码输入框下方显示要求列表
- 实时更新密码强度状态
- 密码不符合要求时禁用创建按钮

### 🔑 管理员重置密码

**位置**: 管理员用户管理页面

**功能**:
- 重置用户密码时的强度验证
- 提示管理员输入符合要求的密码

**界面特性**:
- 弹窗提示密码要求
- 输入验证和错误提示

## 技术实现

### 前端验证

**JavaScript函数**:
```javascript
function validatePassword(password) {
    // 检查长度 >= 8
    // 检查包含大写字母
    // 检查包含小写字母  
    // 检查包含数字
    return boolean;
}
```

**实时检测**:
- `oninput` 事件监听
- 动态更新UI状态
- 按钮启用/禁用控制

### 后端验证

**验证器模块**: `src/utils/passwordValidator.js`

**主要函数**:
```javascript
validatePasswordStrength(password) {
    return {
        isValid: boolean,
        errors: string[]
    };
}
```

**API集成**:
- 修改密码: `POST /change-password`
- 创建用户: `POST /admin/users`
- 重置密码: `POST /admin/users/:userid/reset-password`

## 用户体验

### 🎯 修改密码流程

1. 用户访问修改密码页面
2. 输入当前密码
3. 输入新密码时看到实时强度检测
4. 确认新密码时验证匹配性
5. 只有满足所有要求才能提交

### 🎯 管理员操作流程

1. **创建用户**:
   - 填写用户信息
   - 输入密码时看到强度要求
   - 密码符合要求后才能创建

2. **重置密码**:
   - 点击重置密码按钮
   - 弹窗提示密码要求
   - 输入符合要求的新密码

## 安全优势

### 🛡️ 提升安全性

1. **防止弱密码**: 杜绝常见弱密码如"123456"、"password"
2. **增加复杂度**: 要求多种字符类型组合
3. **统一标准**: 全系统统一的密码策略

### 🛡️ 用户友好

1. **实时反馈**: 用户输入时立即看到密码强度
2. **清晰指导**: 明确显示密码要求
3. **防止错误**: 不符合要求时禁止提交

## 兼容性说明

### 📱 现有用户

- 现有用户的旧密码仍然有效
- 只有在修改密码时才需要遵循新要求
- 管理员可以为用户重置为符合要求的新密码

### 📱 系统升级

- 新密码策略不影响现有登录
- 渐进式安全提升
- 向后兼容设计

## 故障排除

### ❓ 常见问题

**Q: 为什么我的密码被拒绝？**
A: 请确保密码至少8位，包含大小写字母和数字。

**Q: 如何知道密码是否符合要求？**
A: 在输入密码时，系统会实时显示每项要求的满足状态。

**Q: 管理员如何重置用户密码？**
A: 在用户管理页面点击"重置密码"，输入符合强度要求的新密码。

### ❓ 技术支持

如果遇到密码相关问题，请联系系统管理员或查看系统日志获取详细错误信息。
