# 系统管理员默认多角色配置

## 概述

系统现在默认为管理员用户配置了管理员和主任两种权限，使得系统管理员可以在不同角色间切换，既能进行全局系统管理，也能进行部门值班管理。

## 配置详情

### 🎭 默认角色配置

#### **系统管理员 (admin)**
- **主要角色**: 管理员 (`admin`) ⭐
- **次要角色**: 主任 (`director`)
- **登录账号**: `admin` / `admin`
- **用户名称**: 系统管理员

#### **角色权限**
```
管理员权限 (admin):
✅ 系统全局管理
✅ 用户管理 (创建、编辑、删除用户)
✅ 角色管理 (分配多角色权限)
✅ 系统设置和配置
✅ 全部数据访问权限

主任权限 (director):
✅ 部门值班管理
✅ 值班记录管理 (添加、编辑、删除)
✅ 员工值班统计
✅ 值班计划安排
✅ 部门数据访问权限
```

### 🔧 技术实现

#### **数据库结构**
```sql
-- 用户表
users: 
  userid=1, job_number='admin', name='系统管理员', role='admin'

-- 用户角色关联表
user_roles:
  userid=1, role='admin', is_primary=1    -- 主要角色
  userid=1, role='director', is_primary=0 -- 次要角色
```

#### **自动配置逻辑**
```javascript
// 数据库初始化时自动配置
if (adminUser not exists) {
  1. 创建管理员用户
  2. 添加 admin 角色 (主要)
  3. 添加 director 角色 (次要)
} else {
  1. 检查现有角色
  2. 补充缺失的角色
  3. 确保角色完整性
}
```

## 功能特性

### 🔄 角色切换

#### **切换方式**
1. **登录默认**: 以管理员身份登录
2. **右上角菜单**: 点击角色下拉菜单
3. **选择角色**: 选择"主任"切换身份
4. **自动跳转**: 切换后跳转到对应页面

#### **界面显示**
```
导航栏右上角:
┌─────────────────────────────────────────┐
│ 欢迎，系统管理员  [管理员 ▼] 修改密码 退出 │
└─────────────────────────────────────────┘

角色切换下拉菜单:
┌─────────────┐
│ 👑 管理员    │ ← 当前角色
│ 👔 主任      │ ← 可切换
└─────────────┘
```

### 🎯 使用场景

#### **管理员身份使用**
- **用户管理**: 创建、编辑、删除用户账号
- **权限分配**: 为用户分配多个角色
- **系统监控**: 查看系统整体运行状态
- **数据管理**: 访问和管理全部系统数据

#### **主任身份使用**
- **值班安排**: 安排和管理部门值班计划
- **记录管理**: 添加、编辑值班和调休记录
- **员工统计**: 查看员工值班统计信息
- **部门管理**: 管理部门相关的值班事务

### 📊 权限对比

| 功能模块 | 管理员身份 | 主任身份 | 员工身份 |
|----------|------------|----------|----------|
| 用户管理 | ✅ 完全权限 | ❌ 无权限 | ❌ 无权限 |
| 系统设置 | ✅ 完全权限 | ❌ 无权限 | ❌ 无权限 |
| 值班管理 | ✅ 完全权限 | ✅ 完全权限 | ❌ 无权限 |
| 记录编辑 | ✅ 完全权限 | ✅ 完全权限 | ❌ 无权限 |
| 员工统计 | ✅ 完全权限 | ✅ 完全权限 | ❌ 无权限 |
| 个人查看 | ✅ 完全权限 | ✅ 完全权限 | ✅ 个人数据 |

## 配置过程

### 🔨 自动配置

#### **新系统初始化**
```javascript
// 系统首次启动时自动执行
1. 检查是否存在管理员用户
2. 如果不存在，创建默认管理员
3. 自动添加管理员和主任角色
4. 设置管理员为主要角色
5. 完成多角色配置
```

#### **现有系统升级**
```javascript
// 现有系统升级时自动执行
1. 检查现有管理员用户
2. 检查用户角色配置
3. 补充缺失的角色权限
4. 保持数据完整性
5. 完成升级配置
```

### 🛠️ 手动配置

#### **添加角色权限**
```javascript
// 为现有管理员添加主任权限
await User.addUserRole(adminUserId, 'director', false);
```

#### **移除角色权限**
```javascript
// 移除不需要的角色权限
await User.removeUserRole(adminUserId, 'director');
```

## 验证方法

### 🧪 功能测试

#### **登录测试**
1. 使用 `admin/admin` 登录
2. 验证默认以管理员身份登录
3. 检查右上角是否显示角色切换菜单
4. 验证可以切换到主任身份

#### **权限测试**
1. **管理员身份**: 访问用户管理页面
2. **主任身份**: 访问值班管理页面
3. **角色切换**: 验证切换功能正常
4. **权限隔离**: 验证不同角色的权限边界

#### **数据验证**
```sql
-- 检查用户角色配置
SELECT u.name, ur.role, ur.is_primary 
FROM users u 
JOIN user_roles ur ON u.userid = ur.userid 
WHERE u.job_number = 'admin';

-- 预期结果:
-- 系统管理员 | admin    | 1
-- 系统管理员 | director | 0
```

### 📋 验证清单

- ✅ 管理员用户存在
- ✅ 拥有管理员角色 (主要)
- ✅ 拥有主任角色 (次要)
- ✅ 角色切换菜单显示
- ✅ 角色切换功能正常
- ✅ 权限验证正确
- ✅ 数据库配置一致

## 使用指南

### 👨‍💼 管理员操作

#### **日常管理工作**
1. **以管理员身份登录**
   - 管理用户账号
   - 分配用户权限
   - 查看系统统计

2. **切换到主任身份**
   - 点击右上角角色菜单
   - 选择"主任"
   - 管理值班事务

#### **角色切换流程**
```
1. 登录系统 (默认管理员身份)
   ↓
2. 点击右上角 [管理员 ▼]
   ↓
3. 选择 "👔 主任"
   ↓
4. 确认切换
   ↓
5. 自动跳转到主任页面
   ↓
6. 以主任身份工作
```

### 🔧 故障排除

#### **常见问题**

**问题1**: 管理员没有主任权限
```bash
# 解决方案: 运行角色更新脚本
node scripts/update-admin-roles.js
```

**问题2**: 角色切换菜单不显示
```bash
# 解决方案: 检查用户角色数据
node scripts/verify-admin-multi-role.js
```

**问题3**: 切换角色后权限异常
```bash
# 解决方案: 清除浏览器缓存，重新登录
```

## 安全考虑

### 🔒 权限控制

#### **角色隔离**
- 不同角色访问不同的功能模块
- 严格的权限验证机制
- 防止权限越权访问

#### **会话管理**
- JWT Token 包含当前角色信息
- 角色切换时更新 Token
- 会话过期时需要重新登录

### 🛡️ 安全建议

1. **定期更改密码**: 建议定期更改管理员密码
2. **权限审计**: 定期检查用户权限分配
3. **操作日志**: 记录重要操作的审计日志
4. **访问控制**: 限制管理员账号的访问来源

## 总结

系统管理员现在默认拥有管理员和主任两种权限：

### ✅ 配置完成
- 🎭 **双重身份**: 管理员 + 主任权限
- 🔄 **灵活切换**: 右上角一键切换角色
- 🎯 **权限精确**: 不同角色不同权限范围
- 🔧 **自动配置**: 系统启动时自动配置

### 🎯 使用价值
- **管理效率**: 一个账号多种身份，提高管理效率
- **权限灵活**: 根据工作需要切换合适的角色
- **系统完整**: 既能管理系统又能管理业务
- **用户体验**: 无需多个账号，操作更便捷

这个配置使得系统管理员可以更加灵活地管理整个值班管理系统，既能进行系统级的管理工作，也能参与具体的值班管理业务！
