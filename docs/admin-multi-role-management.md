# 管理员多角色用户管理功能

## 概述

管理员用户管理界面现在完全支持多角色用户的显示、创建和编辑功能。管理员可以为用户分配多个角色，查看用户的所有角色信息，并进行灵活的角色管理。

## 功能特性

### 🎭 多角色显示

#### **用户列表增强**
- **角色标签**: 显示用户的所有角色，使用不同颜色的标签
- **主要角色标识**: 主要角色标签带有星标 (⭐) 标识
- **角色数量**: 显示用户拥有的角色总数
- **多角色徽章**: 多角色用户显示特殊的徽章标识

#### **角色标签样式**
- **管理员**: 红色标签 (`is-danger`)
- **主任**: 黄色标签 (`is-warning`) 
- **员工**: 蓝色标签 (`is-info`)
- **主要角色**: 深色标签 + 星标
- **次要角色**: 浅色标签 (`is-light`)

### 📊 统计信息增强

#### **新增统计卡片**
- **多角色用户**: 显示拥有多个角色的用户数量
- **角色分布**: 按主要角色分类的用户统计
- **用户总览**: 总用户数和各角色用户数

#### **统计布局**
```
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│   总用户数   │   管理员    │    主任     │    员工     │ 多角色用户  │
│      5      │      1      │      1      │      3      │      2      │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

### ✏️ 多角色编辑

#### **创建用户界面**
- **复选框选择**: 使用复选框支持多角色选择
- **角色标签**: 每个选项显示对应的角色标签
- **主要角色**: 第一个选择的角色自动成为主要角色
- **提示信息**: 清晰的操作说明和提示

#### **编辑用户界面**
- **当前角色显示**: 显示用户当前拥有的所有角色
- **主要角色标识**: 当前主要角色带有星标标识
- **角色修改**: 可以添加或移除角色
- **角色重排**: 重新选择的第一个角色成为新的主要角色

### 🔧 技术实现

#### **后端API增强**

**用户列表API** (`GET /admin/users-page`):
```javascript
// 获取用户及其角色信息
const usersWithRoles = await Promise.all(
  allUsers.map(async (user) => {
    const roles = await User.getUserRoles(user.userid);
    return {
      ...user,
      roles: roles,
      availableRoles: roles.map(r => r.role),
      primaryRole: roles.find(r => r.is_primary)?.role,
      roleCount: roles.length
    };
  })
);
```

**用户详情API** (`GET /admin/users/:userid`):
```javascript
// 返回用户详细信息包含角色
{
  userid: 1,
  name: "李老师",
  job_number: "S001",
  roles: [
    { role: "staff", is_primary: true },
    { role: "director", is_primary: false }
  ],
  availableRoles: ["staff", "director"],
  primaryRole: "staff"
}
```

**用户创建API** (`POST /admin/users`):
```javascript
// 支持多角色创建
{
  name: "新用户",
  jobNumber: "S004",
  roles: ["staff", "director"],  // 多角色数组
  password: "password123"
}
```

**用户更新API** (`PUT /admin/users/:userid`):
```javascript
// 支持多角色更新
{
  name: "用户名",
  jobNumber: "S001", 
  roles: ["staff", "admin"]  // 新的角色组合
}
```

#### **前端界面实现**

**多角色显示模板**:
```html
<td>
  <% user.roles.forEach((roleInfo) => { %>
    <span class="tag is-info <%= roleInfo.is_primary ? '' : 'is-light' %>">
      <% if (roleInfo.is_primary) { %>
        <i class="fas fa-star"></i>
      <% } %>
      员工
    </span>
  <% }) %>
</td>
```

**角色选择界面**:
```html
<div class="field">
  <label class="checkbox">
    <input type="checkbox" id="roleStaff" value="staff">
    <span class="tag is-info">员工</span>
  </label>
</div>
```

**JavaScript角色处理**:
```javascript
// 获取选中的角色
const selectedRoles = [];
if (document.getElementById('roleStaff').checked) selectedRoles.push('staff');
if (document.getElementById('roleDirector').checked) selectedRoles.push('director');
if (document.getElementById('roleAdmin').checked) selectedRoles.push('admin');
```

## 用户界面

### 📋 用户列表界面

#### **表格列结构**
| 用户ID | 工号 | 姓名 | 角色 | 角色数量 | 创建时间 | 操作 |
|--------|------|------|------|----------|----------|------|
| 1 | S001 | 李老师 | ⭐员工 主任 | 👥2 | 2024-06-01 | 编辑 重置密码 删除 |

#### **角色显示示例**
- **单角色用户**: `⭐员工`
- **多角色用户**: `⭐员工 主任` 或 `⭐主任 管理员`
- **角色数量**: 单角色显示 `1`，多角色显示 `👥2`

### ✏️ 编辑界面

#### **角色选择区域**
```
角色选择
ℹ️ 可以选择多个角色，第一个选择的角色将作为主要角色

☑️ 员工    (当前主要角色 ⭐)
☑️ 主任
☐ 管理员

⚠️ ⭐ 标记表示当前的主要角色
```

#### **操作流程**
1. **查看当前角色**: 显示用户当前拥有的所有角色
2. **修改角色**: 勾选或取消勾选角色复选框
3. **主要角色**: 第一个勾选的角色成为主要角色
4. **保存更改**: 更新用户的角色配置

## 数据管理

### 🗄️ 数据结构

#### **用户角色关联表**
```sql
user_roles:
- id (主键)
- userid (用户ID)
- role (角色: staff/director/admin)
- is_primary (是否为主要角色)
- created_at (创建时间)
```

#### **角色管理逻辑**
1. **创建用户**: 第一个角色自动设为主要角色
2. **编辑角色**: 清除现有角色，重新分配
3. **主要角色**: 确保每个用户有且仅有一个主要角色
4. **角色删除**: 级联删除用户角色关联

### 🔄 角色同步

#### **数据一致性**
- **users表**: 保存主要角色信息
- **user_roles表**: 保存完整角色信息
- **自动同步**: 角色变更时自动更新两个表

#### **兼容性处理**
- **向后兼容**: 支持原有的单角色API
- **渐进升级**: 新功能不影响现有功能
- **数据迁移**: 自动迁移现有用户角色数据

## 使用场景

### 👥 典型管理场景

#### **场景1: 创建多角色用户**
1. 点击"添加用户"按钮
2. 填写基本信息（姓名、工号、密码）
3. 选择多个角色（如：员工 + 主任）
4. 第一个选择的角色成为主要角色
5. 保存创建用户

#### **场景2: 调整用户角色**
1. 在用户列表中点击"编辑"
2. 查看用户当前角色配置
3. 修改角色选择（添加或移除角色）
4. 调整主要角色（重新排序选择）
5. 保存更改

#### **场景3: 查看多角色统计**
1. 查看统计卡片了解多角色用户数量
2. 在用户列表中识别多角色用户
3. 查看角色分布和用户分类
4. 进行角色管理决策

### 🎯 管理价值

#### **灵活性提升**
- **角色组合**: 支持任意角色组合
- **权限精确**: 精确控制用户权限范围
- **管理简化**: 减少账号数量，简化管理

#### **可视化增强**
- **直观显示**: 清晰显示用户角色信息
- **状态标识**: 明确的主要角色标识
- **统计分析**: 完整的角色分布统计

## 总结

管理员多角色用户管理功能为系统带来了强大的用户管理能力：

### ✅ 核心功能
- **多角色显示**: 完整显示用户的所有角色信息
- **多角色编辑**: 灵活的角色分配和修改功能
- **主要角色管理**: 清晰的主要角色标识和管理
- **统计分析**: 全面的多角色用户统计

### 🎯 用户体验
- **直观界面**: 清晰的角色标签和标识
- **操作简便**: 简单的复选框多选界面
- **信息完整**: 完整的角色信息展示
- **反馈及时**: 实时的操作反馈和提示

### 🔧 技术优势
- **数据完整**: 完整的多角色数据模型
- **API丰富**: 全面的角色管理API
- **兼容性好**: 向后兼容现有功能
- **扩展性强**: 易于扩展新的角色类型

这个功能使得管理员可以更加灵活和精确地管理用户权限，适应复杂的组织结构和权限需求。
