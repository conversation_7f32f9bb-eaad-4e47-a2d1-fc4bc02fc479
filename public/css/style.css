/* 自定义样式 - <PERSON>ul<PERSON> 兼容 */
body {
    background-color: #f8f9fa;
}

/* 导航栏优化 - Bulma 样式 */
.navbar.is-primary {
    padding: 0.3rem 0 !important;
    min-height: 65px !important;
    height: 65px !important;
}

.navbar .navbar-brand .navbar-item {
    font-weight: bold !important;
    font-size: 1.8rem !important;
    padding: 0.4rem 0.75rem !important;
    line-height: 1.1 !important;
    color: white !important;
}

.navbar .navbar-brand .navbar-item:hover {
    background-color: transparent !important;
    color: white !important;
}

.navbar .navbar-menu .navbar-item {
    padding: 0.4rem 0.4rem !important;
    font-size: 0.9rem !important;
    line-height: 1.1 !important;
    color: white !important;
}

.navbar .navbar-menu .navbar-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* 确保导航栏容器紧凑 */
.navbar .container {
    padding: 0 15px !important;
    height: 100% !important;
}

/* 响应式导航栏优化 */
@media (max-width: 768px) {
    .navbar .navbar-brand .navbar-item {
        font-size: 0.9rem !important;
    }

    .navbar .navbar-menu .navbar-item {
        font-size: 0.85rem !important;
        padding: 0.15rem 0.5rem !important;
    }
}

/* Bulma 卡片样式增强 */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.25rem 2rem !important; /* 增加内边距：上下 1.25rem，左右 2rem */
    min-height: 4rem !important; /* 设置最小高度 */
}

/* Bulma 表格样式 */
.table th {
    border-top: none;
    font-weight: 600;
}

/* Bulma 按钮样式 */
.button.is-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Bulma 颜色类 */
.has-text-primary {
    color: #3273dc !important;
}

.has-text-success {
    color: #23d160 !important;
}

.has-text-info {
    color: #209cee !important;
}

.has-text-warning {
    color: #ffdd57 !important;
}

.has-text-danger {
    color: #ff3860 !important;
}

.has-text-grey {
    color: #7a7a7a !important;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

/* 主内容区域 */
.container {
    min-height: calc(100vh - 200px);
}

/* 减少主内容区域的上边距 */
.container.mt-4 {
    margin-top: 1.5rem !important;
}

/* 年份月份选择器样式 */
.level-right .field.is-grouped {
    margin-left: 1rem; /* 在年份选择前添加左边距 */
}

.level-right .field.is-grouped .control:first-child {
    margin-left: 0.5rem; /* 第一个选择器（年份）前的额外空间 */
}

/* 角色切换下拉菜单样式修复 */
.navbar.is-primary .navbar-dropdown {
    background-color: white !important;
    border: 1px solid #dbdbdb !important;
    border-radius: 6px !important;
    box-shadow: 0 8px 16px rgba(10, 10, 10, 0.1) !important;
    margin-top: 0.5rem !important;
}

.navbar.is-primary .navbar-dropdown .navbar-item {
    color: #363636 !important;
    background-color: white !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
}

.navbar.is-primary .navbar-dropdown .navbar-item:hover {
    background-color: #f5f5f5 !important;
    color: #3273dc !important;
}

.navbar.is-primary .navbar-dropdown .navbar-item .icon {
    color: #7a7a7a !important;
    margin-right: 0.5rem !important;
}

.navbar.is-primary .navbar-dropdown .navbar-item:hover .icon {
    color: #3273dc !important;
}

/* 角色切换按钮样式 */
.navbar.is-primary .navbar-link {
    color: white !important;
    background-color: transparent !important;
    border: none !important;
}

.navbar.is-primary .navbar-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.navbar.is-primary .navbar-link::after {
    border-color: white !important;
}

/* Bulma自带的下拉箭头样式已通过 .navbar-link::after 设置 */

/* 角色图标样式 */
.navbar.is-primary .navbar-link .fa-user-tag {
    color: white !important;
    margin-right: 0.5rem !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-content {
        padding: 1rem;
    }

    .table {
        font-size: 0.875rem;
    }

    .level-right .field.is-grouped {
        margin-left: 0.5rem; /* 移动端减少边距 */
    }

    /* 移动端下拉菜单调整 */
    .navbar.is-primary .navbar-dropdown {
        margin-top: 0.25rem !important;
        min-width: 200px !important;
    }

    .navbar.is-primary .navbar-dropdown .navbar-item {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.85rem !important;
    }
}
