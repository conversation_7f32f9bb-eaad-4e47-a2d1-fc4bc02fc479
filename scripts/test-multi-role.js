const User = require('../src/models/User');

async function testMultiRoleFeatures() {
  try {
    console.log('🧪 测试多角色功能...\n');

    // 1. 测试获取用户角色
    console.log('📋 测试获取用户角色：');
    
    const liTeacher = await User.getByJobNumber('S001');
    if (liTeacher) {
      const roles = await User.getUserRoles(liTeacher.userid);
      console.log(`   李老师的角色:`);
      roles.forEach(role => {
        const primary = role.is_primary ? ' (主要)' : '';
        console.log(`   - ${role.role}${primary}`);
      });
    }

    const wangTeacher = await User.getByJobNumber('S002');
    if (wangTeacher) {
      const roles = await User.getUserRoles(wangTeacher.userid);
      console.log(`   王老师的角色:`);
      roles.forEach(role => {
        const primary = role.is_primary ? ' (主要)' : '';
        console.log(`   - ${role.role}${primary}`);
      });
    }

    // 2. 测试带角色信息的用户查询
    console.log('\n🔍 测试带角色信息的用户查询：');
    
    const userWithRoles = await User.findByJobNumberWithRoles('S001');
    if (userWithRoles) {
      console.log(`   用户: ${userWithRoles.name}`);
      console.log(`   主要角色: ${userWithRoles.primaryRole}`);
      console.log(`   可用角色: ${userWithRoles.availableRoles.join(', ')}`);
      console.log(`   当前角色: ${userWithRoles.current_role || '未设置'}`);
    }

    // 3. 测试角色切换
    console.log('\n🔄 测试角色切换：');
    
    if (liTeacher) {
      // 切换到主任角色
      await User.updateCurrentRole(liTeacher.userid, 'director');
      console.log(`   ✅ 李老师切换到主任角色`);
      
      // 验证切换结果
      const updatedUser = await User.getById(liTeacher.userid);
      console.log(`   当前角色: ${updatedUser.current_role}`);
      
      // 切换回员工角色
      await User.updateCurrentRole(liTeacher.userid, 'staff');
      console.log(`   ✅ 李老师切换回员工角色`);
    }

    // 4. 测试角色权限验证
    console.log('\n🔐 测试角色权限验证：');
    
    if (wangTeacher) {
      const roles = await User.getUserRoles(wangTeacher.userid);
      const hasAdminRole = roles.some(r => r.role === 'admin');
      const hasDirectorRole = roles.some(r => r.role === 'director');
      const hasStaffRole = roles.some(r => r.role === 'staff');
      
      console.log(`   王老师权限检查:`);
      console.log(`   - 管理员权限: ${hasAdminRole ? '✅' : '❌'}`);
      console.log(`   - 主任权限: ${hasDirectorRole ? '✅' : '❌'}`);
      console.log(`   - 员工权限: ${hasStaffRole ? '✅' : '❌'}`);
    }

    // 5. 测试添加和移除角色
    console.log('\n➕ 测试添加和移除角色：');
    
    const zhaoTeacher = await User.getByJobNumber('S003');
    if (zhaoTeacher) {
      // 添加主任角色
      await User.addUserRole(zhaoTeacher.userid, 'director', false);
      console.log(`   ✅ 为赵老师添加主任角色`);
      
      // 验证添加结果
      const rolesAfterAdd = await User.getUserRoles(zhaoTeacher.userid);
      console.log(`   赵老师现在的角色: ${rolesAfterAdd.map(r => r.role).join(', ')}`);
      
      // 移除主任角色
      await User.removeUserRole(zhaoTeacher.userid, 'director');
      console.log(`   ✅ 移除赵老师的主任角色`);
      
      // 验证移除结果
      const rolesAfterRemove = await User.getUserRoles(zhaoTeacher.userid);
      console.log(`   赵老师现在的角色: ${rolesAfterRemove.map(r => r.role).join(', ')}`);
    }

    // 6. 统计多角色用户
    console.log('\n📊 多角色用户统计：');
    
    const allUsers = await User.getAll();
    let multiRoleCount = 0;
    
    for (const user of allUsers) {
      const roles = await User.getUserRoles(user.userid);
      if (roles.length > 1) {
        multiRoleCount++;
        console.log(`   ${user.name}: ${roles.map(r => r.role).join(', ')}`);
      }
    }
    
    console.log(`   总计 ${multiRoleCount} 个用户拥有多个角色`);

    // 7. 验证数据完整性
    console.log('\n✅ 验证数据完整性：');
    
    let allValid = true;
    
    for (const user of allUsers) {
      const roles = await User.getUserRoles(user.userid);
      const primaryRoles = roles.filter(r => r.is_primary);
      
      if (primaryRoles.length !== 1) {
        console.log(`   ❌ ${user.name} 的主要角色数量异常: ${primaryRoles.length}`);
        allValid = false;
      }
      
      if (roles.length === 0) {
        console.log(`   ❌ ${user.name} 没有任何角色`);
        allValid = false;
      }
    }
    
    if (allValid) {
      console.log(`   ✅ 所有用户的角色数据完整性验证通过`);
    }

    console.log('\n🎉 多角色功能测试完成！');
    
    console.log('\n📝 功能总结：');
    console.log('   ✅ 用户可以拥有多个角色');
    console.log('   ✅ 每个用户有一个主要角色');
    console.log('   ✅ 支持动态角色切换');
    console.log('   ✅ 角色权限验证正常');
    console.log('   ✅ 角色添加和移除功能正常');
    console.log('   ✅ 数据完整性验证通过');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testMultiRoleFeatures();
