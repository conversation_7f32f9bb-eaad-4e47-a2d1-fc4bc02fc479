const { validatePasswordStrength, getPasswordStrengthDescription } = require('../src/utils/passwordValidator');

console.log('🔐 测试密码强度验证器...\n');

// 测试用例
const testPasswords = [
  { password: '123456', description: '纯数字，长度不足' },
  { password: 'password', description: '纯小写字母，无数字' },
  { password: 'Password', description: '大小写字母，无数字' },
  { password: 'Password123', description: '符合所有要求' },
  { password: 'MyPass1', description: '长度不足' },
  { password: 'MYPASSWORD123', description: '无小写字母' },
  { password: 'mypassword123', description: '无大写字母' },
  { password: 'MyPassword', description: '无数字' },
  { password: 'MySecurePass123', description: '符合所有要求（长密码）' },
  { password: '', description: '空密码' },
  { password: 'Aa1', description: '长度太短' },
  { password: 'Admin123', description: '符合所有要求' }
];

console.log('┌─────────────────────┬──────────┬─────────────────────────────────────────┐');
console.log('│       密码          │   结果   │                 描述                    │');
console.log('├─────────────────────┼──────────┼─────────────────────────────────────────┤');

testPasswords.forEach(test => {
  const result = validatePasswordStrength(test.password);
  const isValid = result.isValid ? '✅ 通过' : '❌ 失败';
  const password = test.password.padEnd(19);
  const status = isValid.padEnd(8);
  const description = test.description.padEnd(39);
  
  console.log(`│ ${password} │ ${status} │ ${description} │`);
  
  if (!result.isValid && result.errors.length > 0) {
    console.log(`│                     │          │ 错误: ${result.errors.join('，').padEnd(31)} │`);
  }
});

console.log('└─────────────────────┴──────────┴─────────────────────────────────────────┘');

console.log('\n📋 详细测试结果：\n');

testPasswords.forEach((test, index) => {
  console.log(`${index + 1}. 密码: "${test.password}"`);
  console.log(`   描述: ${test.description}`);
  
  const result = validatePasswordStrength(test.password);
  console.log(`   结果: ${result.isValid ? '✅ 通过' : '❌ 失败'}`);
  
  if (!result.isValid) {
    console.log(`   错误: ${result.errors.join('，')}`);
  }
  
  console.log(`   强度描述: ${getPasswordStrengthDescription(test.password)}`);
  console.log('');
});

console.log('🎉 密码强度验证器测试完成！');
