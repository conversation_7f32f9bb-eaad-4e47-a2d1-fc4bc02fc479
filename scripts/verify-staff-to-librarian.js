const fs = require('fs');
const path = require('path');

function verifyStaffToLibrarian() {
  console.log('🔍 验证"员工"到"馆员"的更改...\n');

  const filesToCheck = [
    // 前端模板文件
    'views/admin/dashboard.ejs',
    'views/admin/users.ejs', 
    'views/director/dashboard.ejs',
    'views/partials/header.ejs',
    
    // JavaScript文件
    'public/js/main.js'
  ];

  const searchTerms = [
    { old: '员工', new: '馆员', context: '统计卡片和标签' },
    { old: '老师', new: '馆员', context: 'JavaScript和部分模板' }
  ];

  let totalIssues = 0;
  let totalChecked = 0;

  console.log('📁 检查的文件列表：');
  filesToCheck.forEach((file, index) => {
    console.log(`   ${index + 1}. ${file}`);
  });
  console.log('');

  filesToCheck.forEach(filePath => {
    const fullPath = path.join(__dirname, '..', filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      return;
    }

    console.log(`🔍 检查文件: ${filePath}`);
    const content = fs.readFileSync(fullPath, 'utf8');
    const lines = content.split('\n');
    
    let fileIssues = 0;
    
    searchTerms.forEach(term => {
      lines.forEach((line, lineIndex) => {
        if (line.includes(term.old)) {
          // 检查是否是注释或者特殊情况
          const isComment = line.trim().startsWith('//') || line.trim().startsWith('<!--');
          const isInString = line.includes(`'${term.old}'`) || line.includes(`"${term.old}"`);
          
          if (!isComment) {
            console.log(`   ⚠️  第${lineIndex + 1}行发现"${term.old}": ${line.trim()}`);
            fileIssues++;
            totalIssues++;
          }
        }
      });
    });

    if (fileIssues === 0) {
      console.log(`   ✅ 文件检查通过，未发现遗留的"员工"或"老师"字样`);
    } else {
      console.log(`   ❌ 文件发现 ${fileIssues} 个问题`);
    }
    
    totalChecked++;
    console.log('');
  });

  // 检查特定的更改点
  console.log('🎯 检查关键更改点：');
  
  const keyChanges = [
    {
      file: 'views/admin/dashboard.ejs',
      checks: [
        { line: '馆员数', description: '统计卡片标题' },
        { line: 'roleDisplay.*馆员', description: '多角色用户详情显示' },
        { line: 'tag.*馆员', description: '用户列表角色标签' }
      ]
    },
    {
      file: 'views/admin/users.ejs', 
      checks: [
        { line: '馆员', description: '用户管理页面角色显示' },
        { line: 'tag.*馆员', description: '角色标签' }
      ]
    },
    {
      file: 'views/director/dashboard.ejs',
      checks: [
        { line: '馆员总数', description: '主任页面统计' },
        { line: '馆员管理', description: '主任页面标题' }
      ]
    },
    {
      file: 'views/partials/header.ejs',
      checks: [
        { line: 'staff.*馆员', description: '角色切换下拉菜单' }
      ]
    },
    {
      file: 'public/js/main.js',
      checks: [
        { line: '馆员没有.*权限', description: '权限提示信息' },
        { line: '选择馆员', description: '表单标签' },
        { line: '获取馆员', description: '错误提示' },
        { line: 'tag.*馆员', description: '角色选择标签' }
      ]
    }
  ];

  let keyChangesPassed = 0;
  let totalKeyChecks = 0;

  keyChanges.forEach(fileCheck => {
    const fullPath = path.join(__dirname, '..', fileCheck.file);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ 关键文件不存在: ${fileCheck.file}`);
      return;
    }

    console.log(`📋 检查 ${fileCheck.file}:`);
    const content = fs.readFileSync(fullPath, 'utf8');
    
    fileCheck.checks.forEach(check => {
      totalKeyChecks++;
      const regex = new RegExp(check.line, 'i');
      
      if (regex.test(content)) {
        console.log(`   ✅ ${check.description}: 已更新`);
        keyChangesPassed++;
      } else {
        console.log(`   ❌ ${check.description}: 未找到更新`);
      }
    });
    console.log('');
  });

  // 生成报告
  console.log('📊 检查报告：');
  console.log('═'.repeat(50));
  
  console.log(`📁 文件检查:`);
  console.log(`   检查文件数: ${totalChecked}`);
  console.log(`   发现问题数: ${totalIssues}`);
  console.log(`   检查状态: ${totalIssues === 0 ? '✅ 通过' : '❌ 有问题'}`);
  
  console.log(`\n🎯 关键更改检查:`);
  console.log(`   检查项目数: ${totalKeyChecks}`);
  console.log(`   通过项目数: ${keyChangesPassed}`);
  console.log(`   通过率: ${((keyChangesPassed / totalKeyChecks) * 100).toFixed(1)}%`);
  console.log(`   检查状态: ${keyChangesPassed === totalKeyChecks ? '✅ 通过' : '❌ 有问题'}`);

  console.log(`\n🏆 总体评估:`);
  const overallPassed = totalIssues === 0 && keyChangesPassed === totalKeyChecks;
  console.log(`   状态: ${overallPassed ? '✅ 全部通过' : '❌ 需要修复'}`);
  
  if (overallPassed) {
    console.log(`\n🎉 恭喜！所有"员工"已成功更改为"馆员"`);
    console.log(`\n📝 更改总结:`);
    console.log(`   • 管理员首页统计卡片: 员工数 → 馆员数`);
    console.log(`   • 多角色用户详情: 员工 → 馆员`);
    console.log(`   • 用户列表角色标签: 老师 → 馆员`);
    console.log(`   • 角色切换下拉菜单: 员工 → 馆员`);
    console.log(`   • 主任页面统计: 员工总数 → 馆员总数`);
    console.log(`   • 主任页面标题: 员工管理 → 馆员管理`);
    console.log(`   • JavaScript提示信息: 老师 → 馆员`);
    console.log(`   • 表单和模态框: 老师 → 馆员`);
  } else {
    console.log(`\n🔧 需要修复的问题:`);
    if (totalIssues > 0) {
      console.log(`   • 还有 ${totalIssues} 处遗留的"员工"或"老师"字样`);
    }
    if (keyChangesPassed < totalKeyChecks) {
      console.log(`   • 关键更改点有 ${totalKeyChecks - keyChangesPassed} 项未完成`);
    }
  }

  console.log(`\n🌟 界面效果预览:`);
  console.log(`   统计卡片: [总用户数] [馆员数] [主任数] [管理员数]`);
  console.log(`   角色标签: 🏷️ 馆员  🏷️ 主任  🏷️ 管理员`);
  console.log(`   切换菜单: 👤 馆员  👔 主任  👑 管理员`);
  console.log(`   页面标题: "馆员管理" "馆员总数" "选择馆员"`);
}

// 运行验证
verifyStaffToLibrarian();
