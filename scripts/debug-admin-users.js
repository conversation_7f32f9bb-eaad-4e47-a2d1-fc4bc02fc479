const User = require('../src/models/User');

async function debugAdminUsersData() {
  try {
    console.log('🔍 调试管理员用户管理数据...\n');

    // 1. 获取所有用户的原始数据
    console.log('📋 获取原始用户数据：');
    const allUsers = await User.getAll();
    console.log(`   原始用户数量: ${allUsers.length}`);
    
    allUsers.forEach(user => {
      console.log(`   - ${user.name} (${user.job_number}): role=${user.role}, userid=${user.userid}`);
    });

    // 2. 为每个用户获取角色信息
    console.log('\n🎭 获取用户角色信息：');
    const usersWithRoles = await Promise.all(
      allUsers.map(async (user) => {
        const roles = await User.getUserRoles(user.userid);
        const primaryRole = roles.find(r => r.is_primary);
        const firstRole = roles[0];
        
        const userData = {
          ...user,
          roles: roles,
          availableRoles: roles.map(r => r.role),
          primaryRole: primaryRole ? primaryRole.role : (firstRole ? firstRole.role : null),
          roleCount: roles.length
        };
        
        console.log(`   ${user.name}:`);
        console.log(`     原始角色: ${user.role}`);
        console.log(`     角色详情: ${JSON.stringify(roles)}`);
        console.log(`     可用角色: ${userData.availableRoles.join(', ')}`);
        console.log(`     主要角色: ${userData.primaryRole}`);
        console.log(`     角色数量: ${userData.roleCount}`);
        console.log('');
        
        return userData;
      })
    );

    // 3. 计算统计信息
    console.log('📊 计算统计信息：');
    const staff = usersWithRoles.filter(u => u.primaryRole === 'staff');
    const directors = usersWithRoles.filter(u => u.primaryRole === 'director');
    const admins = usersWithRoles.filter(u => u.primaryRole === 'admin');
    const multiRoleUsers = usersWithRoles.filter(u => u.roleCount > 1);

    console.log(`   员工 (主要角色): ${staff.length} 人`);
    console.log(`   主任 (主要角色): ${directors.length} 人`);
    console.log(`   管理员 (主要角色): ${admins.length} 人`);
    console.log(`   多角色用户: ${multiRoleUsers.length} 人`);

    // 4. 检查模板数据结构
    console.log('\n🖥️ 模拟模板数据结构：');
    const templateData = {
      title: '用户管理',
      user: { userid: 1, name: '管理员', role: 'admin' }, // 模拟当前用户
      users: {
        all: usersWithRoles,
        staff,
        directors,
        admins,
        multiRole: multiRoleUsers
      }
    };

    console.log('   模板数据结构:');
    console.log(`     users.all.length: ${templateData.users.all.length}`);
    console.log(`     users.staff.length: ${templateData.users.staff.length}`);
    console.log(`     users.directors.length: ${templateData.users.directors.length}`);
    console.log(`     users.admins.length: ${templateData.users.admins.length}`);
    console.log(`     users.multiRole.length: ${templateData.users.multiRole.length}`);

    // 5. 检查每个用户的角色显示数据
    console.log('\n🏷️ 检查角色显示数据：');
    templateData.users.all.forEach(user => {
      console.log(`   ${user.name} (${user.job_number}):`);
      
      if (user.roles && user.roles.length > 0) {
        console.log(`     ✅ 有角色数据 (${user.roles.length} 个)`);
        user.roles.forEach(roleInfo => {
          const roleDisplay = roleInfo.role === 'admin' ? '管理员' : 
                             roleInfo.role === 'director' ? '主任' : '老师';
          const primaryMark = roleInfo.is_primary ? ' ⭐' : '';
          console.log(`       - ${roleDisplay}${primaryMark}`);
        });
      } else {
        console.log(`     ❌ 无角色数据，使用fallback: ${user.role}`);
      }
      
      console.log(`     角色数量: ${user.roleCount || 0}`);
      console.log('');
    });

    // 6. 测试用户详情API
    console.log('🔍 测试用户详情API：');
    if (usersWithRoles.length > 0) {
      const testUser = usersWithRoles[0];
      console.log(`   测试用户: ${testUser.name} (userid: ${testUser.userid})`);
      
      try {
        const userDetails = await User.getById(testUser.userid);
        const userRoles = await User.getUserRoles(testUser.userid);
        
        console.log(`   ✅ 基本信息获取成功`);
        console.log(`   ✅ 角色信息获取成功 (${userRoles.length} 个角色)`);
        
        const primaryRole = userRoles.find(r => r.is_primary);
        const firstRole = userRoles[0];
        
        const apiResponse = {
          ...userDetails,
          roles: userRoles,
          availableRoles: userRoles.map(r => r.role),
          primaryRole: primaryRole ? primaryRole.role : (firstRole ? firstRole.role : null)
        };
        
        console.log(`   API响应数据:`, JSON.stringify(apiResponse, null, 2));
        
      } catch (error) {
        console.log(`   ❌ API测试失败: ${error.message}`);
      }
    }

    // 7. 检查数据库连接和查询
    console.log('\n🗄️ 检查数据库查询：');
    
    try {
      // 测试基本查询
      const testQuery = await User.getAll();
      console.log(`   ✅ User.getAll() 成功 (${testQuery.length} 条记录)`);
      
      // 测试角色查询
      if (testQuery.length > 0) {
        const testRoles = await User.getUserRoles(testQuery[0].userid);
        console.log(`   ✅ User.getUserRoles() 成功 (${testRoles.length} 条角色记录)`);
      }
      
    } catch (error) {
      console.log(`   ❌ 数据库查询失败: ${error.message}`);
    }

    // 8. 总结问题
    console.log('\n🎯 问题诊断总结：');
    
    const issues = [];
    
    // 检查是否有用户没有角色数据
    const usersWithoutRoles = usersWithRoles.filter(u => !u.roles || u.roles.length === 0);
    if (usersWithoutRoles.length > 0) {
      issues.push(`${usersWithoutRoles.length} 个用户没有角色数据`);
    }
    
    // 检查多角色统计是否正确
    const actualMultiRole = usersWithRoles.filter(u => u.roleCount > 1).length;
    if (actualMultiRole !== multiRoleUsers.length) {
      issues.push(`多角色统计不一致: 实际${actualMultiRole}, 计算${multiRoleUsers.length}`);
    }
    
    // 检查主要角色是否都有值
    const usersWithoutPrimaryRole = usersWithRoles.filter(u => !u.primaryRole);
    if (usersWithoutPrimaryRole.length > 0) {
      issues.push(`${usersWithoutPrimaryRole.length} 个用户没有主要角色`);
    }
    
    if (issues.length === 0) {
      console.log('   ✅ 数据结构正常，没有发现问题');
    } else {
      console.log('   ❌ 发现以下问题:');
      issues.forEach(issue => console.log(`     - ${issue}`));
    }

    console.log('\n🔧 修复建议：');
    console.log('   1. 重启应用服务器以确保代码更改生效');
    console.log('   2. 清除浏览器缓存');
    console.log('   3. 检查浏览器开发者工具的网络和控制台');
    console.log('   4. 验证数据库中的 user_roles 表数据');

    console.log('\n✨ 调试完成！');

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
}

// 运行调试
debugAdminUsersData();
