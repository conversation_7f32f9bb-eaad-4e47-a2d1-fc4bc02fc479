const ExcelJS = require('exceljs');

async function createTestExcel() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('用户数据');
  
  // 设置列标题
  worksheet.columns = [
    { header: '工号', key: 'job_number', width: 15 },
    { header: '姓名', key: 'name', width: 15 },
    { header: '密码', key: 'password', width: 15 },
    { header: '角色', key: 'role', width: 15 }
  ];
  
  // 添加测试数据
  const testUsers = [
    { job_number: 'T001', name: '测试老师1', password: '123456', role: 'staff' },
    { job_number: 'T002', name: '测试老师2', password: '123456', role: 'staff' },
    { job_number: 'T003', name: '测试主任1', password: '123456', role: 'director' },
    { job_number: 'T004', name: '测试老师3', password: '123456', role: 'staff' }
  ];
  
  testUsers.forEach(user => {
    worksheet.addRow(user);
  });
  
  // 保存文件
  await workbook.xlsx.writeFile('test-users.xlsx');
  console.log('测试Excel文件已创建: test-users.xlsx');
}

createTestExcel().catch(console.error);
