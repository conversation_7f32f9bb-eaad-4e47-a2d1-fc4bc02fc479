const User = require('../src/models/User');

async function verifyAdminMultiRole() {
  try {
    console.log('🔍 验证系统管理员多角色配置...\n');

    // 1. 查找系统管理员用户
    console.log('👤 查找系统管理员用户：');
    const adminUser = await User.findByJobNumberWithRoles('admin');
    
    if (!adminUser) {
      console.log('   ❌ 未找到系统管理员用户');
      return;
    }

    console.log(`   ✅ 找到用户: ${adminUser.name} (${adminUser.job_number})`);
    console.log(`   用户ID: ${adminUser.userid}`);
    console.log(`   主要角色: ${adminUser.primaryRole}`);
    console.log(`   当前角色: ${adminUser.current_role || '未设置'}`);

    // 2. 显示所有角色
    console.log('\n🎭 用户角色详情：');
    console.log(`   角色总数: ${adminUser.roles.length}`);
    console.log(`   可用角色: ${adminUser.availableRoles.join(', ')}`);
    
    console.log('\n   角色列表:');
    adminUser.roles.forEach((role, index) => {
      const roleDisplay = role.role === 'admin' ? '管理员' : 
                         role.role === 'director' ? '主任' : 
                         role.role === 'staff' ? '员工' : role.role;
      const primaryMark = role.is_primary ? ' ⭐(主要角色)' : ' (次要角色)';
      console.log(`     ${index + 1}. ${roleDisplay}${primaryMark}`);
    });

    // 3. 验证角色权限
    console.log('\n🔐 角色权限验证：');
    
    const hasAdminRole = adminUser.availableRoles.includes('admin');
    const hasDirectorRole = adminUser.availableRoles.includes('director');
    const hasStaffRole = adminUser.availableRoles.includes('staff');
    
    console.log(`   管理员权限: ${hasAdminRole ? '✅ 拥有' : '❌ 缺失'}`);
    console.log(`   主任权限: ${hasDirectorRole ? '✅ 拥有' : '❌ 缺失'}`);
    console.log(`   员工权限: ${hasStaffRole ? '✅ 拥有' : '❌ 缺失'}`);

    // 4. 验证主要角色设置
    console.log('\n⭐ 主要角色验证：');
    const primaryRoles = adminUser.roles.filter(r => r.is_primary);
    
    if (primaryRoles.length === 1) {
      console.log(`   ✅ 主要角色设置正确: ${primaryRoles[0].role}`);
    } else if (primaryRoles.length === 0) {
      console.log(`   ❌ 没有设置主要角色`);
    } else {
      console.log(`   ❌ 主要角色设置异常: ${primaryRoles.length} 个主要角色`);
    }

    // 5. 模拟角色切换功能
    console.log('\n🔄 角色切换功能验证：');
    
    if (adminUser.availableRoles.length > 1) {
      console.log('   ✅ 支持角色切换');
      console.log('   可切换的角色:');
      adminUser.availableRoles.forEach(role => {
        const roleDisplay = role === 'admin' ? '管理员' : 
                           role === 'director' ? '主任' : 
                           role === 'staff' ? '员工' : role;
        const isCurrent = role === adminUser.primaryRole;
        const mark = isCurrent ? ' (当前)' : '';
        console.log(`     - ${roleDisplay}${mark}`);
      });
    } else {
      console.log('   ❌ 只有一个角色，无法切换');
    }

    // 6. 验证数据库一致性
    console.log('\n🗄️ 数据库一致性验证：');
    
    // 检查users表中的role字段
    const userRecord = await User.getById(adminUser.userid);
    console.log(`   users表中的role字段: ${userRecord.role}`);
    console.log(`   主要角色匹配: ${userRecord.role === adminUser.primaryRole ? '✅ 一致' : '❌ 不一致'}`);

    // 7. 功能权限测试
    console.log('\n🛠️ 功能权限测试：');
    
    const permissions = {
      '系统管理': hasAdminRole,
      '用户管理': hasAdminRole,
      '全局设置': hasAdminRole,
      '值班管理': hasDirectorRole || hasAdminRole,
      '员工管理': hasDirectorRole || hasAdminRole,
      '记录编辑': hasDirectorRole || hasAdminRole,
      '个人查看': hasStaffRole || hasDirectorRole || hasAdminRole
    };

    Object.entries(permissions).forEach(([permission, hasPermission]) => {
      console.log(`   ${permission}: ${hasPermission ? '✅ 有权限' : '❌ 无权限'}`);
    });

    // 8. 登录测试模拟
    console.log('\n🔑 登录行为模拟：');
    
    console.log('   登录流程:');
    console.log(`     1. 用户输入: admin / admin`);
    console.log(`     2. 验证成功，获取用户信息`);
    console.log(`     3. 加载角色: ${adminUser.availableRoles.join(', ')}`);
    console.log(`     4. 设置当前角色: ${adminUser.primaryRole} (主要角色)`);
    console.log(`     5. 跳转到: /admin/dashboard`);
    console.log(`     6. 右上角显示角色切换菜单`);

    // 9. 界面显示预览
    console.log('\n🖥️ 界面显示预览：');
    
    console.log('   导航栏右上角:');
    console.log('   ┌─────────────────────────────────────────┐');
    console.log('   │ 欢迎，系统管理员  [管理员 ▼] 修改密码 退出 │');
    console.log('   └─────────────────────────────────────────┘');
    
    console.log('\n   角色切换下拉菜单:');
    console.log('   ┌─────────────┐');
    console.log('   │ 👑 管理员    │ ← 当前角色');
    console.log('   │ 👔 主任      │ ← 可切换');
    console.log('   └─────────────┘');

    // 10. 总结
    console.log('\n📋 配置总结：');
    
    const summary = [
      `✅ 系统管理员拥有 ${adminUser.roles.length} 个角色`,
      `✅ 主要角色: ${adminUser.primaryRole}`,
      `✅ 次要角色: ${adminUser.availableRoles.filter(r => r !== adminUser.primaryRole).join(', ')}`,
      `✅ 支持角色切换功能`,
      `✅ 数据库配置正确`,
      `✅ 权限设置完整`
    ];

    summary.forEach(item => console.log(`   ${item}`));

    console.log('\n🎯 使用说明：');
    console.log('   • 使用 admin/admin 登录系统');
    console.log('   • 默认以管理员身份登录');
    console.log('   • 可在右上角切换到主任身份');
    console.log('   • 管理员身份：全局系统管理');
    console.log('   • 主任身份：部门值班管理');
    console.log('   • 切换角色后页面自动跳转');

    console.log('\n✨ 系统管理员多角色配置验证完成！');

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  }
}

// 运行验证
verifyAdminMultiRole();
