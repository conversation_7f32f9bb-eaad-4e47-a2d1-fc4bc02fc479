const bcrypt = require('bcryptjs');
const User = require('../src/models/User');
const { validatePasswordStrength } = require('../src/utils/passwordValidator');

async function testPasswordAPI() {
  try {
    console.log('🔐 测试密码API功能...\n');

    // 1. 测试密码强度验证器
    console.log('1. 测试密码强度验证器：');
    
    const testPasswords = [
      'weak',           // 弱密码
      'Password123',    // 强密码
      'NoNumber',       // 无数字
      'nonumber123',    // 无大写字母
      'NOLOWER123'      // 无小写字母
    ];

    testPasswords.forEach(password => {
      const result = validatePasswordStrength(password);
      console.log(`   密码 "${password}": ${result.isValid ? '✅ 通过' : '❌ 失败'}`);
      if (!result.isValid) {
        console.log(`      错误: ${result.errors.join('，')}`);
      }
    });

    // 2. 测试用户密码更新
    console.log('\n2. 测试用户密码更新：');
    
    // 获取一个测试用户
    const testUser = await User.getByJobNumber('S001');
    if (!testUser) {
      console.log('   ❌ 找不到测试用户 S001');
      return;
    }
    
    console.log(`   找到测试用户: ${testUser.name} (${testUser.job_number})`);

    // 测试更新为强密码
    const strongPassword = 'NewPassword123';
    const strongValidation = validatePasswordStrength(strongPassword);
    
    if (strongValidation.isValid) {
      await User.updatePassword(testUser.userid, strongPassword);
      console.log(`   ✅ 成功更新为强密码: ${strongPassword}`);
      
      // 验证密码是否正确更新
      const updatedUser = await User.getById(testUser.userid);
      const isMatch = await bcrypt.compare(strongPassword, updatedUser.password);
      console.log(`   ✅ 密码验证: ${isMatch ? '正确' : '错误'}`);
    } else {
      console.log(`   ❌ 强密码验证失败: ${strongValidation.errors.join('，')}`);
    }

    // 3. 测试弱密码拒绝
    console.log('\n3. 测试弱密码拒绝：');
    
    const weakPasswords = ['123456', 'password', 'Password'];
    
    for (const weakPassword of weakPasswords) {
      const validation = validatePasswordStrength(weakPassword);
      if (!validation.isValid) {
        console.log(`   ✅ 正确拒绝弱密码 "${weakPassword}": ${validation.errors.join('，')}`);
      } else {
        console.log(`   ❌ 错误接受了弱密码 "${weakPassword}"`);
      }
    }

    // 4. 恢复原始密码
    console.log('\n4. 恢复原始密码：');
    await User.updatePassword(testUser.userid, '123456');
    console.log('   ✅ 已恢复测试用户的原始密码');

    console.log('\n🎉 密码API测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testPasswordAPI();
