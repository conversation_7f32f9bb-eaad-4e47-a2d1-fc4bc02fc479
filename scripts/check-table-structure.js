const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置 - 从.env文件读取
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

async function checkTableStructure() {
  let connection;

  try {
    console.log('🔍 检查数据库表结构...\n');

    // 连接到数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 连接到数据库成功');

    // 检查 duty_records 表结构
    console.log('\n📋 duty_records 表结构:');
    const [columns] = await connection.execute('DESCRIBE duty_records');
    
    console.log('┌─────────────────┬─────────────────┬──────┬─────┬─────────┬────────┐');
    console.log('│      字段       │      类型       │ 空值 │ 键  │  默认值 │  额外  │');
    console.log('├─────────────────┼─────────────────┼──────┼─────┼─────────┼────────┤');
    
    columns.forEach(column => {
      const field = column.Field.padEnd(15);
      const type = column.Type.padEnd(15);
      const nullable = column.Null.padEnd(4);
      const key = column.Key.padEnd(3);
      const defaultValue = (column.Default || '').toString().padEnd(7);
      const extra = column.Extra.padEnd(6);
      
      console.log(`│ ${field} │ ${type} │ ${nullable} │ ${key} │ ${defaultValue} │ ${extra} │`);
    });
    
    console.log('└─────────────────┴─────────────────┴──────┴─────┴─────────┴────────┘');

    // 检查备注字段的数据
    console.log('\n📝 检查记录数据（包含请假记录和节假日值班）:');
    const [records] = await connection.execute(`
      SELECT dr.record_id, u.name, dr.duty_date, dr.leave_date, dr.leave_end_date, dr.is_leave_record, dr.is_holiday_duty, dr.remarks
      FROM duty_records dr
      JOIN users u ON dr.userid = u.userid
      ORDER BY dr.created_at DESC
      LIMIT 10
    `);

    if (records.length > 0) {
      console.log('┌────────┬──────────┬─────────────┬─────────────┬─────────────┬──────────┬──────────────────────────────┐');
      console.log('│ 记录ID │   姓名   │  值班日期   │  调休开始   │  调休结束   │   类型   │            备注              │');
      console.log('├────────┼──────────┼─────────────┼─────────────┼─────────────┼──────────┼──────────────────────────────┤');

      records.forEach(record => {
        const id = record.record_id.toString().padEnd(6);
        const name = record.name.padEnd(8);
        const dutyDate = record.duty_date ? record.duty_date.toISOString().split('T')[0].padEnd(11) : '     -     '.padEnd(11);
        const leaveDate = record.leave_date ? record.leave_date.toISOString().split('T')[0].padEnd(11) : '     -     '.padEnd(11);
        const leaveEndDate = record.leave_end_date ? record.leave_end_date.toISOString().split('T')[0].padEnd(11) : '     -     '.padEnd(11);

        let type = '';
        if (record.is_leave_record) {
          type = '请假'.padEnd(8);
        } else if (record.is_holiday_duty) {
          type = '节假日值班'.padEnd(8);
        } else {
          type = '值班'.padEnd(8);
        }

        const remarks = (record.remarks || '').padEnd(28);

        console.log(`│ ${id} │ ${name} │ ${dutyDate} │ ${leaveDate} │ ${leaveEndDate} │ ${type} │ ${remarks} │`);
      });

      console.log('└────────┴──────────┴─────────────┴─────────────┴─────────────┴──────────┴──────────────────────────────┘');
    } else {
      console.log('   📝 暂无值班记录数据');
    }

    console.log('\n🎉 表结构检查完成！');

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkTableStructure();
