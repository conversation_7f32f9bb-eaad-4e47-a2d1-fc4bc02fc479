const DutyRecord = require('../src/models/DutyRecord');

async function testQueries() {
  try {
    console.log('🔍 测试查询功能...\n');

    // 测试获取所有记录（2024年8月）
    console.log('📅 测试获取2024年8月的所有记录:');
    const records2024_08 = await DutyRecord.getAll(2024, 8);
    console.log(`   找到 ${records2024_08.length} 条记录`);
    records2024_08.forEach(record => {
      const type = record.is_leave_record ? '请假' : '值班';
      const date = record.is_leave_record ? 
        (record.leave_date ? record.leave_date.toISOString().split('T')[0] : 'null') :
        (record.duty_date ? record.duty_date.toISOString().split('T')[0] : 'null');
      console.log(`   - ${record.name}: ${type} (${date}) - ${record.remarks || '无备注'}`);
    });

    // 测试获取所有记录（2025年5月）
    console.log('\n📅 测试获取2025年5月的所有记录:');
    const records2025_05 = await DutyRecord.getAll(2025, 5);
    console.log(`   找到 ${records2025_05.length} 条记录`);
    records2025_05.forEach(record => {
      const type = record.is_leave_record ? '请假' : '值班';
      const date = record.is_leave_record ? 
        (record.leave_date ? record.leave_date.toISOString().split('T')[0] : 'null') :
        (record.duty_date ? record.duty_date.toISOString().split('T')[0] : 'null');
      console.log(`   - ${record.name}: ${type} (${date}) - ${record.remarks || '无备注'}`);
    });

    // 测试获取用户记录（用户ID 3 = 李老师）
    console.log('\n👤 测试获取李老师(userid=3)的2025年记录:');
    const userRecords = await DutyRecord.getByUserId(3, 2025);
    console.log(`   找到 ${userRecords.length} 条记录`);
    userRecords.forEach(record => {
      const type = record.is_leave_record ? '请假' : '值班';
      const date = record.is_leave_record ? 
        (record.leave_date ? record.leave_date.toISOString().split('T')[0] : 'null') :
        (record.duty_date ? record.duty_date.toISOString().split('T')[0] : 'null');
      console.log(`   - ${type} (${date}) - ${record.remarks || '无备注'}`);
    });

    console.log('\n✅ 查询测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testQueries();
