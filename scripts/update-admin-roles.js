const { pool } = require('../src/utils/database');

async function updateAdminRoles() {
  let connection;
  
  try {
    connection = await pool.getConnection();
    console.log('🔧 更新系统管理员角色权限...\n');

    // 1. 查找系统管理员用户
    console.log('🔍 查找系统管理员用户...');
    const [adminUsers] = await connection.execute(
      'SELECT * FROM users WHERE job_number = ? OR role = ?',
      ['admin', 'admin']
    );

    if (adminUsers.length === 0) {
      console.log('   ❌ 未找到系统管理员用户');
      return;
    }

    console.log(`   ✅ 找到 ${adminUsers.length} 个管理员用户`);

    for (const adminUser of adminUsers) {
      console.log(`\n👤 处理用户: ${adminUser.name} (${adminUser.job_number})`);
      
      // 2. 检查用户当前的角色
      const [currentRoles] = await connection.execute(
        'SELECT * FROM user_roles WHERE userid = ?',
        [adminUser.userid]
      );

      console.log(`   当前角色数量: ${currentRoles.length}`);
      currentRoles.forEach(role => {
        const primaryMark = role.is_primary ? ' (主要)' : '';
        console.log(`     - ${role.role}${primaryMark}`);
      });

      // 3. 如果没有角色记录，创建初始角色
      if (currentRoles.length === 0) {
        console.log('   📝 创建初始角色记录...');
        
        // 添加管理员角色作为主要角色
        await connection.execute(
          'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
          [adminUser.userid, 'admin', true]
        );
        console.log('     ✅ 添加管理员角色 (主要)');
        
        // 添加主任角色作为次要角色
        await connection.execute(
          'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
          [adminUser.userid, 'director', false]
        );
        console.log('     ✅ 添加主任角色 (次要)');
      } 
      // 4. 如果只有管理员角色，添加主任角色
      else if (currentRoles.length === 1 && currentRoles[0].role === 'admin') {
        console.log('   📝 添加主任角色...');
        
        await connection.execute(
          'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
          [adminUser.userid, 'director', false]
        );
        console.log('     ✅ 添加主任角色 (次要)');
      }
      // 5. 如果已经有主任角色，检查是否需要调整
      else {
        const hasAdminRole = currentRoles.some(r => r.role === 'admin');
        const hasDirectorRole = currentRoles.some(r => r.role === 'director');
        
        if (!hasAdminRole) {
          console.log('   📝 添加管理员角色...');
          await connection.execute(
            'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
            [adminUser.userid, 'admin', true]
          );
          console.log('     ✅ 添加管理员角色 (主要)');
        }
        
        if (!hasDirectorRole) {
          console.log('   📝 添加主任角色...');
          await connection.execute(
            'INSERT INTO user_roles (userid, role, is_primary) VALUES (?, ?, ?)',
            [adminUser.userid, 'director', false]
          );
          console.log('     ✅ 添加主任角色 (次要)');
        }
        
        if (hasAdminRole && hasDirectorRole) {
          console.log('   ✅ 用户已拥有管理员和主任角色');
        }
      }

      // 6. 更新用户名称（如果需要）
      if (adminUser.name === 'Administrator') {
        await connection.execute(
          'UPDATE users SET name = ? WHERE userid = ?',
          ['系统管理员', adminUser.userid]
        );
        console.log('   ✅ 更新用户名称为"系统管理员"');
      }
    }

    // 7. 验证更新结果
    console.log('\n📊 验证更新结果：');
    
    for (const adminUser of adminUsers) {
      const [updatedRoles] = await connection.execute(
        'SELECT * FROM user_roles WHERE userid = ? ORDER BY is_primary DESC, role',
        [adminUser.userid]
      );

      const [updatedUser] = await connection.execute(
        'SELECT * FROM users WHERE userid = ?',
        [adminUser.userid]
      );

      console.log(`\n   ${updatedUser[0].name} (${updatedUser[0].job_number}):`);
      console.log(`     角色数量: ${updatedRoles.length}`);
      updatedRoles.forEach(role => {
        const primaryMark = role.is_primary ? ' ⭐(主要)' : '';
        const roleDisplay = role.role === 'admin' ? '管理员' : 
                           role.role === 'director' ? '主任' : 
                           role.role === 'staff' ? '员工' : role.role;
        console.log(`     - ${roleDisplay}${primaryMark}`);
      });
    }

    // 8. 统计信息
    console.log('\n📈 统计信息：');
    
    const [totalMultiRoleAdmins] = await connection.execute(`
      SELECT COUNT(DISTINCT ur.userid) as count
      FROM user_roles ur
      JOIN users u ON ur.userid = u.userid
      WHERE u.role = 'admin' AND ur.userid IN (
        SELECT userid FROM user_roles GROUP BY userid HAVING COUNT(*) > 1
      )
    `);

    console.log(`   多角色管理员用户: ${totalMultiRoleAdmins[0].count} 人`);

    const [adminWithDirector] = await connection.execute(`
      SELECT COUNT(DISTINCT ur1.userid) as count
      FROM user_roles ur1
      JOIN user_roles ur2 ON ur1.userid = ur2.userid
      WHERE ur1.role = 'admin' AND ur2.role = 'director'
    `);

    console.log(`   拥有管理员+主任权限: ${adminWithDirector[0].count} 人`);

    console.log('\n🎉 系统管理员角色权限更新完成！');
    
    console.log('\n📝 更新说明：');
    console.log('   • 系统管理员现在同时拥有管理员和主任权限');
    console.log('   • 管理员权限为主要角色，主任权限为次要角色');
    console.log('   • 可以在右上角切换角色身份');
    console.log('   • 管理员身份：系统全局管理');
    console.log('   • 主任身份：部门值班管理');

  } catch (error) {
    console.error('❌ 更新管理员角色时发生错误:', error);
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

// 运行更新
updateAdminRoles();
