const { pool } = require('../src/utils/database');
const bcrypt = require('bcrypt');

async function createDefaultUsers() {
  try {
    console.log('👥 开始创建默认用户...\n');
    
    // 检查是否已有用户
    const [existingUsers] = await pool.execute('SELECT COUNT(*) as count FROM users');
    if (existingUsers[0].count > 0) {
      console.log('⚠️  数据库中已存在用户，是否要清空并重新创建？');
      console.log('   如需重新创建，请先运行: node scripts/init-database.js\n');
      return;
    }
    
    // 创建默认用户
    const users = [
      {
        job_number: 'admin',
        name: '系统管理员',
        password: 'admin',
        role: 'admin',
        department: '管理部'
      },
      {
        job_number: 'D001',
        name: '张主任',
        password: '123456',
        role: 'director',
        department: '技术部'
      },
      {
        job_number: 'S001',
        name: '李老师',
        password: '123456',
        role: 'staff',
        department: '技术部'
      },
      {
        job_number: 'S002',
        name: '王老师',
        password: '123456',
        role: 'staff',
        department: '技术部'
      },
      {
        job_number: 'S003',
        name: '赵老师',
        password: '123456',
        role: 'staff',
        department: '技术部'
      }
    ];
    
    console.log('🔐 创建用户账号...');
    
    for (const user of users) {
      // 加密密码
      const hashedPassword = await bcrypt.hash(user.password, 10);
      
      // 插入用户
      await pool.execute(
        'INSERT INTO users (job_number, name, password, role, department) VALUES (?, ?, ?, ?, ?)',
        [user.job_number, user.name, hashedPassword, user.role, user.department]
      );
      
      const roleText = user.role === 'admin' ? '管理员' :
                      user.role === 'director' ? '主任' : '老师';
      console.log(`   ✅ ${roleText}: ${user.job_number} / ${user.password} (${user.name})`);
    }
    
    console.log('\n🎉 默认用户创建完成！\n');
    
    console.log('📋 登录账号信息:');
    console.log('┌─────────────┬──────────┬──────────┬──────────┬──────────┐');
    console.log('│    角色     │   工号   │   密码   │   姓名   │   部门   │');
    console.log('├─────────────┼──────────┼──────────┼──────────┼──────────┤');
    console.log('│ 系统管理员  │  admin   │  admin   │系统管理员│  管理部  │');
    console.log('│    主任     │  D001    │ 123456   │  张主任  │  技术部  │');
    console.log('│    老师     │  S001    │ 123456   │  李老师  │  技术部  │');
    console.log('│    老师     │  S002    │ 123456   │  王老师  │  技术部  │');
    console.log('│    老师     │  S003    │ 123456   │  赵老师  │  技术部  │');
    console.log('└─────────────┴──────────┴──────────┴──────────┴──────────┘\n');
    
    console.log('📝 下一步:');
    console.log('   1. 启动应用: node src/app.js');
    console.log('   2. 访问: http://localhost:3000');
    console.log('   3. 使用上述账号登录系统\n');
    
  } catch (error) {
    console.error('❌ 创建默认用户失败:', error.message);
    
    if (error.code === 'ER_DUP_ENTRY') {
      console.log('\n💡 用户已存在，如需重新创建请先清空用户表');
    } else if (error.code === 'ER_NO_SUCH_TABLE') {
      console.log('\n💡 请先运行数据库初始化: node scripts/init-database.js');
    }
    
    process.exit(1);
  } finally {
    if (pool) {
      await pool.end();
    }
  }
}

// 运行创建默认用户
createDefaultUsers();
