const { pool } = require('../src/utils/database');

async function addMultiRoleSupport() {
  let connection;
  
  try {
    connection = await pool.getConnection();
    console.log('🔄 开始添加多角色支持...\n');

    // 1. 创建用户角色关联表
    console.log('📋 创建用户角色关联表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        userid INT NOT NULL,
        role ENUM('staff', 'director', 'admin') NOT NULL,
        is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要角色',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userid) REFERENCES users(userid) ON DELETE CASCADE,
        UNIQUE KEY unique_user_role (userid, role),
        INDEX idx_userid (userid),
        INDEX idx_role (role)
      ) COMMENT='用户角色关联表'
    `);

    // 2. 迁移现有用户的角色数据
    console.log('📊 迁移现有用户角色数据...');
    
    // 获取所有现有用户
    const [existingUsers] = await connection.execute('SELECT userid, role FROM users');
    
    for (const user of existingUsers) {
      // 将现有角色作为主要角色插入到新表中
      await connection.execute(
        'INSERT IGNORE INTO user_roles (userid, role, is_primary) VALUES (?, ?, TRUE)',
        [user.userid, user.role]
      );
    }

    console.log(`   ✅ 已迁移 ${existingUsers.length} 个用户的角色数据`);

    // 3. 添加一些示例多角色用户
    console.log('👥 创建示例多角色用户...');
    
    // 为李老师添加主任权限（既是员工又是主任）
    const [liTeacher] = await connection.execute('SELECT userid FROM users WHERE job_number = ?', ['S001']);
    if (liTeacher.length > 0) {
      await connection.execute(
        'INSERT IGNORE INTO user_roles (userid, role, is_primary) VALUES (?, ?, FALSE)',
        [liTeacher[0].userid, 'director']
      );
      console.log('   ✅ 为李老师添加了主任权限');
    }

    // 为王老师添加管理员权限（既是员工又是管理员）
    const [wangTeacher] = await connection.execute('SELECT userid FROM users WHERE job_number = ?', ['S002']);
    if (wangTeacher.length > 0) {
      await connection.execute(
        'INSERT IGNORE INTO user_roles (userid, role, is_primary) VALUES (?, ?, FALSE)',
        [wangTeacher[0].userid, 'admin']
      );
      console.log('   ✅ 为王老师添加了管理员权限');
    }

    // 4. 添加当前角色字段到用户表（用于会话管理）
    console.log('🔧 添加当前角色字段...');
    try {
      await connection.execute(`
        ALTER TABLE users ADD COLUMN current_role ENUM('staff', 'director', 'admin') NULL COMMENT '当前使用的角色'
      `);
      console.log('   ✅ 已添加 current_role 字段');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('   ℹ️ current_role 字段已存在');
      } else {
        throw error;
      }
    }

    // 5. 验证数据
    console.log('\n📊 验证多角色数据：');
    
    const [multiRoleUsers] = await connection.execute(`
      SELECT u.name, u.job_number, ur.role, ur.is_primary
      FROM users u
      JOIN user_roles ur ON u.userid = ur.userid
      ORDER BY u.name, ur.is_primary DESC, ur.role
    `);

    console.log('┌──────────┬──────┬──────────┬──────────┐');
    console.log('│   姓名   │ 工号 │   角色   │ 主要角色 │');
    console.log('├──────────┼──────┼──────────┼──────────┤');
    
    multiRoleUsers.forEach(user => {
      const name = user.name.padEnd(8);
      const jobNumber = user.job_number.padEnd(4);
      const role = user.role.padEnd(8);
      const isPrimary = user.is_primary ? '✓' : ' ';
      console.log(`│ ${name} │ ${jobNumber} │ ${role} │    ${isPrimary}     │`);
    });
    
    console.log('└──────────┴──────┴──────────┴──────────┘');

    // 6. 统计信息
    const [roleStats] = await connection.execute(`
      SELECT 
        COUNT(DISTINCT userid) as total_users,
        COUNT(*) as total_roles,
        COUNT(*) - COUNT(DISTINCT userid) as additional_roles
      FROM user_roles
    `);

    console.log('\n📈 统计信息：');
    console.log(`   总用户数: ${roleStats[0].total_users}`);
    console.log(`   总角色数: ${roleStats[0].total_roles}`);
    console.log(`   额外角色: ${roleStats[0].additional_roles}`);

    console.log('\n🎉 多角色支持添加完成！');
    console.log('\n📝 功能说明：');
    console.log('   • 用户可以拥有多个角色');
    console.log('   • 每个用户有一个主要角色（登录时的默认角色）');
    console.log('   • 可以在界面上切换不同角色身份');
    console.log('   • 李老师现在既是员工又是主任');
    console.log('   • 王老师现在既是员工又是管理员');

  } catch (error) {
    console.error('❌ 添加多角色支持时发生错误:', error);
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

// 运行脚本
addMultiRoleSupport();
