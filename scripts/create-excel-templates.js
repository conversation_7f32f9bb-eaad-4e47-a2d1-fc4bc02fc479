const ExcelJS = require('exceljs');
const path = require('path');

async function createUserTemplate() {
  console.log('📋 创建用户导入模板...');
  
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('用户导入模板');

  // 设置列标题
  worksheet.columns = [
    { header: '工号', key: 'job_number', width: 15 },
    { header: '姓名', key: 'name', width: 15 },
    { header: '密码', key: 'password', width: 15 },
    { header: '角色', key: 'role', width: 15 }
  ];

  // 设置标题行样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE6F3FF' }
  };

  // 添加示例数据
  worksheet.addRow({
    job_number: 'S004',
    name: '示例老师',
    password: '123456',
    role: 'staff'
  });

  worksheet.addRow({
    job_number: 'D002',
    name: '示例主任',
    password: '123456',
    role: 'director'
  });

  // 添加说明
  worksheet.addRow({});
  const instructionRow = worksheet.addRow({ job_number: '📋 导入说明：' });
  instructionRow.font = { bold: true };
  
  worksheet.addRow({ job_number: '1. 工号必须唯一，不能与现有用户重复' });
  worksheet.addRow({ job_number: '2. 角色可选值：staff(老师)、director(主任)、admin(管理员)' });
  worksheet.addRow({ job_number: '3. 密码建议使用强密码，至少6位字符' });
  worksheet.addRow({ job_number: '4. 请删除示例数据后再导入' });
  worksheet.addRow({ job_number: '5. 支持批量导入，一次可导入多个用户' });

  // 保存文件
  const templatePath = path.join(__dirname, '../public/templates/user_import_template.xlsx');
  await workbook.xlsx.writeFile(templatePath);
  console.log('✅ 用户导入模板创建成功');
}

async function createDutyTemplate() {
  console.log('📅 创建值班导入模板...');
  
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('值班安排导入模板');

  // 设置列标题
  worksheet.columns = [
    { header: '工号', key: 'job_number', width: 15 },
    { header: '姓名', key: 'name', width: 15 },
    { header: '值班日期', key: 'duty_date', width: 15 },
    { header: '调休日期', key: 'leave_date', width: 15 },
   // { header: '是否自定义调休', key: 'is_custom_leave', width: 20 }
  ];

  // 设置标题行样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
  fgColor: { argb: 'FFE6F3FF' }
  };

  // 添加示例数据
  worksheet.addRow({
    job_number: 'S001',
    name: '张三',
    duty_date: '2024-06-15',
    leave_date: '2024-06-16',
  //  is_custom_leave: '是'
  });

  worksheet.addRow({
    job_number: 'S002',
    name: '李四',
    duty_date: '2024-06-20',
    leave_date: '',
  //  is_custom_leave: '否'
  });

  worksheet.addRow({
    job_number: 'S003',
    name: '王五',
    duty_date: '2024-06-25',
    leave_date: '2024-06-28',
  //  is_custom_leave: '是'
  });

  // 添加说明
  worksheet.addRow({});
  const instructionRow = worksheet.addRow({ job_number: '📋 导入说明：' });
  instructionRow.font = { bold: true, color: { argb: 'FF0066CC' } };
  
  worksheet.addRow({ job_number: '1. 工号必须是系统中已存在的用户' });
  worksheet.addRow({ job_number: '2. 值班日期格式：YYYY-MM-DD（如：2024-06-15）' });
  worksheet.addRow({ job_number: '3. 调休日期可以为空，表示暂未安排调休' });
  //worksheet.addRow({ job_number: '4. 是否自定义调休：填写"是"或"否"' });
  worksheet.addRow({ job_number: '4. 姓名列仅供参考，系统以工号为准' });
  worksheet.addRow({ job_number: '5. 请删除示例数据后再导入' });
  worksheet.addRow({ job_number: '6. 支持批量导入，一次可导入多条值班记录' });

  // 保存文件
  const templatePath = path.join(__dirname, '../public/templates/duty_import_template.xlsx');
  await workbook.xlsx.writeFile(templatePath);
  console.log('✅ 值班导入模板创建成功');
}

async function createTemplates() {
  try {
    console.log('🚀 开始创建Excel模板文件...\n');
    
    await createUserTemplate();
    await createDutyTemplate();
    
    console.log('\n🎉 所有模板文件创建完成！');
    console.log('\n📁 模板文件位置:');
    console.log('   • 用户导入模板: public/templates/user_import_template.xlsx');
    console.log('   • 值班导入模板: public/templates/duty_import_template.xlsx');
    
    console.log('\n🌐 下载地址:');
    console.log('   • 用户模板: http://localhost:3000/admin/users/template');
    console.log('   • 值班模板: http://localhost:3000/director/duty-template');
    
  } catch (error) {
    console.error('❌ 创建模板文件失败:', error);
    process.exit(1);
  }
}

// 运行创建模板
createTemplates();
