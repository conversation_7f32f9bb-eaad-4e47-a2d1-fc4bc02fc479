const { pool } = require('../src/utils/database');

async function checkDatabase() {
  try {
    console.log('🔍 检查数据库连接和配置...\n');
    
    // 1. 测试数据库连接
    console.log('📡 测试数据库连接...');
    const [connection] = await pool.execute('SELECT 1 as test');
    console.log('✅ 数据库连接成功\n');
    
    // 2. 检查数据库信息
    console.log('🗄️  数据库信息:');
    const [dbInfo] = await pool.execute('SELECT DATABASE() as current_db, VERSION() as version');
    console.log(`   数据库: ${dbInfo[0].current_db}`);
    console.log(`   版本: ${dbInfo[0].version}\n`);
    
    // 3. 检查表是否存在
    console.log('📋 检查数据表:');
    const [tables] = await pool.execute('SHOW TABLES');
    
    const requiredTables = ['users', 'duty_records'];
    const existingTables = tables.map(row => Object.values(row)[0]);
    
    for (const table of requiredTables) {
      if (existingTables.includes(table)) {
        console.log(`   ✅ ${table} 表存在`);
        
        // 检查表记录数
        const [count] = await pool.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`      记录数: ${count[0].count}`);
      } else {
        console.log(`   ❌ ${table} 表不存在`);
      }
    }
    
    // 4. 检查用户数据
    if (existingTables.includes('users')) {
      console.log('\n👥 用户数据:');

      // 检查是否有department字段
      const [columns] = await pool.execute('DESCRIBE users');
      const hasDepartment = columns.some(col => col.Field === 'department');

      const query = hasDepartment ?
        'SELECT job_number, name, role, department FROM users ORDER BY role, job_number' :
        'SELECT job_number, name, role FROM users ORDER BY role, job_number';

      const [users] = await pool.execute(query);

      if (users.length > 0) {
        if (hasDepartment) {
          console.log('┌──────────┬──────────┬─────────────┬──────────┐');
          console.log('│   工号   │   姓名   │    角色     │   部门   │');
          console.log('├──────────┼──────────┼─────────────┼──────────┤');

          users.forEach(user => {
            const roleText = user.role === 'admin' ? '系统管理员' :
                            user.role === 'director' ? '主任' : '老师';
            console.log(`│ ${user.job_number.padEnd(8)} │ ${user.name.padEnd(8)} │ ${roleText.padEnd(11)} │ ${(user.department || '').padEnd(8)} │`);
          });

          console.log('└──────────┴──────────┴─────────────┴──────────┘');
        } else {
          console.log('┌──────────┬──────────┬─────────────┐');
          console.log('│   工号   │   姓名   │    角色     │');
          console.log('├──────────┼──────────┼─────────────┤');

          users.forEach(user => {
            const roleText = user.role === 'admin' ? '系统管理员' :
                            user.role === 'director' ? '主任' : '老师';
            console.log(`│ ${user.job_number.padEnd(8)} │ ${user.name.padEnd(8)} │ ${roleText.padEnd(11)} │`);
          });

          console.log('└──────────┴──────────┴─────────────┘');
        }
      } else {
        console.log('   📝 暂无用户数据');
      }
    }
    
    // 5. 检查值班记录数据
    if (existingTables.includes('duty_records')) {
      console.log('\n📅 值班记录统计:');
      const [recordStats] = await pool.execute(`
        SELECT 
          COUNT(*) as total_records,
          COUNT(DISTINCT userid) as unique_users,
          COUNT(CASE WHEN leave_date IS NOT NULL THEN 1 END) as with_leave,
          COUNT(CASE WHEN is_custom_leave = 1 THEN 1 END) as custom_leave
        FROM duty_records
      `);
      
      if (recordStats[0].total_records > 0) {
        console.log(`   总记录数: ${recordStats[0].total_records}`);
        console.log(`   参与人数: ${recordStats[0].unique_users}`);
        console.log(`   已安排调休: ${recordStats[0].with_leave}`);
        console.log(`   自定义调休: ${recordStats[0].custom_leave}`);
        
        // 显示最近的记录
        const [recentRecords] = await pool.execute(`
          SELECT u.name, dr.duty_date, dr.leave_date, dr.is_custom_leave
          FROM duty_records dr
          JOIN users u ON dr.userid = u.userid
          ORDER BY dr.duty_date DESC
          LIMIT 5
        `);
        
        if (recentRecords.length > 0) {
          console.log('\n   最近的值班记录:');
          recentRecords.forEach(record => {
            const leaveInfo = record.leave_date ? 
              `调休: ${record.leave_date}` : '未安排调休';
            const customInfo = record.is_custom_leave ? '(自定义)' : '(自动)';
            console.log(`   • ${record.name}: ${record.duty_date} → ${leaveInfo} ${customInfo}`);
          });
        }
      } else {
        console.log('   📝 暂无值班记录');
      }
    }
    
    console.log('\n🎯 系统状态总结:');
    
    if (existingTables.length === requiredTables.length) {
      console.log('✅ 数据库结构完整');
      
      const [userCount] = await pool.execute('SELECT COUNT(*) as count FROM users');
      if (userCount[0].count > 0) {
        console.log('✅ 用户数据已初始化');
        console.log('🚀 系统可以正常使用');
        console.log('\n启动命令: node src/app.js');
      } else {
        console.log('⚠️  缺少用户数据');
        console.log('💡 运行: node scripts/create-default-users.js');
      }
    } else {
      console.log('❌ 数据库结构不完整');
      console.log('💡 运行: node scripts/init-database.js');
    }
    
  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 解决方案:');
      console.log('   1. 检查 src/utils/database.js 中的数据库配置');
      console.log('   2. 确保MySQL用户名和密码正确');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决方案:');
      console.log('   1. 启动MySQL服务');
      console.log('   2. 检查MySQL端口是否为3306');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 解决方案:');
      console.log('   1. 创建数据库: CREATE DATABASE duty_system;');
      console.log('   2. 或运行: node scripts/quick-setup.js');
    }
    
    process.exit(1);
  } finally {
    if (pool) {
      await pool.end();
    }
  }
}

// 运行数据库检查
checkDatabase();
