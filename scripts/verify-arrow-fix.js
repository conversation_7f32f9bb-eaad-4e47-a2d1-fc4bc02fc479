const fs = require('fs');
const path = require('path');

console.log('🔍 验证双箭头问题修复...\n');

// 检查header.ejs文件
const headerPath = path.join(__dirname, '../views/partials/header.ejs');
const headerContent = fs.readFileSync(headerPath, 'utf8');

console.log('📋 检查 header.ejs 文件：');

// 检查是否移除了手动添加的箭头
const hasManualArrow = headerContent.includes('fa-angle-down');
const hasNavbarLink = headerContent.includes('navbar-link');
const hasUserTagIcon = headerContent.includes('fa-user-tag');

console.log(`   navbar-link 类: ${hasNavbarLink ? '✅ 存在' : '❌ 缺失'}`);
console.log(`   用户图标 (fa-user-tag): ${hasUserTagIcon ? '✅ 存在' : '❌ 缺失'}`);
console.log(`   手动箭头 (fa-angle-down): ${hasManualArrow ? '❌ 仍存在（需要移除）' : '✅ 已移除'}`);

// 检查CSS文件
const cssPath = path.join(__dirname, '../public/css/style.css');
const cssContent = fs.readFileSync(cssPath, 'utf8');

console.log('\n🎨 检查 CSS 样式：');

const hasAfterPseudo = cssContent.includes('.navbar-link::after');
const hasManualArrowCSS = cssContent.includes('.fa-angle-down');
const hasBorderColor = cssContent.includes('border-color: white');

console.log(`   Bulma箭头样式 (::after): ${hasAfterPseudo ? '✅ 存在' : '❌ 缺失'}`);
console.log(`   箭头颜色设置: ${hasBorderColor ? '✅ 已设置' : '❌ 未设置'}`);
console.log(`   手动箭头CSS: ${hasManualArrowCSS ? '⚠️ 仍存在（可能是注释）' : '✅ 已清理'}`);

// 分析代码结构
console.log('\n📊 代码结构分析：');

// 统计navbar-link出现次数
const navbarLinkMatches = headerContent.match(/navbar-link/g);
const navbarLinkCount = navbarLinkMatches ? navbarLinkMatches.length : 0;

// 统计图标数量
const iconMatches = headerContent.match(/<i class="fas/g);
const iconCount = iconMatches ? iconMatches.length : 0;

console.log(`   navbar-link 出现次数: ${navbarLinkCount}`);
console.log(`   Font Awesome 图标总数: ${iconCount}`);

// 检查下拉菜单结构
const hasDropdown = headerContent.includes('has-dropdown');
const hasHoverable = headerContent.includes('is-hoverable');
const hasDropdownMenu = headerContent.includes('navbar-dropdown');

console.log(`   下拉菜单容器: ${hasDropdown ? '✅ 存在' : '❌ 缺失'}`);
console.log(`   悬停触发: ${hasHoverable ? '✅ 存在' : '❌ 缺失'}`);
console.log(`   下拉菜单: ${hasDropdownMenu ? '✅ 存在' : '❌ 缺失'}`);

// 检查修复前后的差异
console.log('\n🔄 修复前后对比：');

const expectedStructure = `
修复前（错误）:
<a class="navbar-link">
  <i class="fas fa-user-tag"></i>
  角色名称
  <i class="fas fa-angle-down"></i>  ← 多余的箭头
</a>

修复后（正确）:
<a class="navbar-link">
  <i class="fas fa-user-tag"></i>
  角色名称
  <!-- Bulma自动添加箭头 -->
</a>
`;

console.log(expectedStructure);

// 验证修复效果
console.log('✅ 修复效果验证：');

let fixStatus = 'success';
const issues = [];

if (hasManualArrow) {
    issues.push('仍存在手动添加的 fa-angle-down 图标');
    fixStatus = 'warning';
}

if (!hasAfterPseudo) {
    issues.push('缺少 .navbar-link::after 样式');
    fixStatus = 'error';
}

if (!hasNavbarLink) {
    issues.push('缺少 navbar-link 类');
    fixStatus = 'error';
}

if (fixStatus === 'success') {
    console.log('   🎉 修复成功！');
    console.log('   ✅ 已移除手动添加的箭头图标');
    console.log('   ✅ 保留了Bulma自带的箭头样式');
    console.log('   ✅ 下拉菜单结构完整');
} else if (fixStatus === 'warning') {
    console.log('   ⚠️ 修复基本完成，但有警告：');
    issues.forEach(issue => console.log(`     - ${issue}`));
} else {
    console.log('   ❌ 修复未完成，存在问题：');
    issues.forEach(issue => console.log(`     - ${issue}`));
}

// 技术说明
console.log('\n📚 技术说明：');
console.log('   • Bulma的 navbar-link 类会自动添加向下箭头');
console.log('   • 箭头通过 ::after 伪元素实现');
console.log('   • 可以通过 border-color 属性控制箭头颜色');
console.log('   • 不需要手动添加 fa-angle-down 图标');

// 测试建议
console.log('\n🧪 测试建议：');
console.log('   1. 访问 /test-dropdown-fixed.html 查看修复效果');
console.log('   2. 登录多角色用户测试实际功能');
console.log('   3. 检查不同浏览器的兼容性');
console.log('   4. 验证移动端显示效果');

// 相关文件
console.log('\n📁 相关文件：');
console.log('   • views/partials/header.ejs - 导航栏模板');
console.log('   • public/css/style.css - 样式文件');
console.log('   • public/test-dropdown-fixed.html - 测试页面');

console.log('\n🎯 修复总结：');
console.log('   问题：角色切换下拉菜单显示两个向下箭头');
console.log('   原因：Bulma自带箭头 + 手动添加箭头');
console.log('   解决：移除手动添加的 fa-angle-down 图标');
console.log('   结果：只显示一个箭头，界面更加简洁');

console.log('\n✨ 验证完成！');
