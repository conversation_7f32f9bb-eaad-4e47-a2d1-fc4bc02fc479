const fs = require('fs');
const path = require('path');

console.log('🔍 检查员工界面修改...\n');

// 读取员工界面文件
const staffDashboardPath = path.join(__dirname, '../views/staff/dashboard.ejs');
const content = fs.readFileSync(staffDashboardPath, 'utf8');

// 检查是否包含新增的请假显示功能
const requiredElements = [
  'stats.totalLeaveDays',
  'notification is-info is-light',
  '请假',
  '本年度累计请假天数',
  'fas fa-calendar-times'
];

console.log('📋 检查新增的请假显示功能：');
let allPresent = true;

requiredElements.forEach(element => {
  const found = content.includes(element);
  const status = found ? '✅ 已添加' : '❌ 缺失';
  console.log(`   ${element}: ${status}`);
  
  if (!found) {
    allPresent = false;
  }
});

// 检查保留的统计卡片
const preservedStats = [
  '本年值班',
  '本年调休', 
  '本月值班',
  '剩余调休',
  'stats.yearDutyCount',
  'stats.yearLeaveCount',
  'stats.monthDutyCount',
  'stats.remainingLeaveCount'
];

console.log('\n📋 检查保留的统计卡片：');
let allPreserved = true;

preservedStats.forEach(stat => {
  const found = content.includes(stat);
  const status = found ? '✅ 已保留' : '❌ 缺失';
  console.log(`   ${stat}: ${status}`);
  
  if (!found) {
    allPreserved = false;
  }
});

// 检查条件显示逻辑
const conditionalLogic = [
  'if (stats.totalLeaveDays > 0)',
  '<% } %>'
];

console.log('\n📋 检查条件显示逻辑：');
let logicCorrect = true;

conditionalLogic.forEach(logic => {
  const found = content.includes(logic);
  const status = found ? '✅ 正确' : '❌ 错误';
  console.log(`   ${logic}: ${status}`);
  
  if (!found) {
    logicCorrect = false;
  }
});

// 检查表格显示
const tableElements = [
  '值班日期',
  '调休日期',
  'is_leave_record',
  'tag is-warning',
  'leave_end_date'
];

console.log('\n📋 检查表格显示：');
let tableCorrect = true;

tableElements.forEach(element => {
  const found = content.includes(element);
  const status = found ? '✅ 正确' : '❌ 错误';
  console.log(`   ${element}: ${status}`);
  
  if (!element) {
    tableCorrect = false;
  }
});

// 统计行数和文件大小
const lines = content.split('\n');
console.log(`\n📊 文件统计：`);
console.log(`   总行数: ${lines.length}`);
console.log(`   文件大小: ${(content.length / 1024).toFixed(2)} KB`);

// 检查页面结构
const hasHeader = content.includes('<%- include(\'../partials/header\') %>');
const hasFooter = content.includes('<%- include(\'../partials/footer\') %>');
const hasColumns = content.includes('<div class="columns">');

console.log(`\n🏗️ 页面结构检查：`);
console.log(`   包含头部: ${hasHeader ? '✅' : '❌'}`);
console.log(`   包含尾部: ${hasFooter ? '✅' : '❌'}`);
console.log(`   包含列布局: ${hasColumns ? '✅' : '❌'}`);

// 总结
console.log(`\n🎉 修改总结：`);
if (allPresent && allPreserved && logicCorrect && tableCorrect && hasHeader && hasFooter && hasColumns) {
  console.log('   ✅ 所有修改都已正确完成！');
  console.log('   ✅ 请假天数显示功能已添加');
  console.log('   ✅ 统计卡片功能完整');
  console.log('   ✅ 条件显示逻辑正确');
  console.log('   ✅ 页面结构完整');
} else {
  console.log('   ⚠️ 发现一些问题，请检查上述详情');
}

console.log('\n📝 员工界面现在包含：');
console.log('   1. 四个统计卡片（本年值班、本年调休、本月值班、剩余调休）');
console.log('   2. 请假天数提示（仅在有请假时显示）');
console.log('   3. 年月选择器');
console.log('   4. 值班记录表格（支持请假记录显示）');

console.log('\n✨ 统计逻辑优化：');
console.log('   • 剩余调休 = 值班天数 - 已安排调休天数');
console.log('   • 请假天数单独计算，不影响调休统计');
console.log('   • 请假记录在表格中显示为"请假"标签');
console.log('   • 请假天数支持多天请假（开始日期至结束日期）');
