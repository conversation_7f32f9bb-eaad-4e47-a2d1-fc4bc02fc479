const { pool } = require('../src/utils/database');

async function addTestRecords() {
  try {
    // 添加一些不同年份的测试记录
    const testRecords = [
      // 2024年记录
      { userid: 3, duty_date: '2024-01-15', leave_date: '2024-01-16', remarks: '新年值班' },
      { userid: 4, duty_date: '2024-02-20', leave_date: '2024-02-21', remarks: '春节期间' },
      { userid: 5, duty_date: '2024-03-10', leave_date: '2024-03-11', remarks: '' },
      { userid: 3, duty_date: '2024-06-15', leave_date: '2024-06-16', remarks: '端午节值班' },
      { userid: 4, duty_date: '2024-09-10', leave_date: '2024-09-11', remarks: '中秋节前' },
      { userid: 5, duty_date: '2024-12-25', leave_date: '2024-12-26', remarks: '圣诞节值班' },

      // 2025年记录
      { userid: 3, duty_date: '2025-02-10', leave_date: '2025-02-11', remarks: '春节值班' },
      { userid: 4, duty_date: '2025-05-15', leave_date: '2025-05-16', remarks: '劳动节后' },
      { userid: 5, duty_date: '2025-08-20', leave_date: '2025-08-21', remarks: '暑期值班' },
      { userid: 3, duty_date: '2025-11-12', leave_date: '2025-11-13', remarks: '秋季值班' }
    ];
    
    for (const record of testRecords) {
      await pool.execute(
        'INSERT INTO duty_records (userid, duty_date, leave_date, is_custom_leave, remarks) VALUES (?, ?, ?, ?, ?)',
        [record.userid, record.duty_date, record.leave_date, true, record.remarks]
      );
      console.log(`Added record: User ${record.userid}, ${record.duty_date}`);
    }
    
    console.log('\n=== Test records added successfully! ===');
    console.log('Records added for years: 2024, 2025');
    console.log('Users: S001 (userid=3), S002 (userid=4), S003 (userid=5)');
    
  } catch (error) {
    console.error('Error adding test records:', error);
  } finally {
    if (pool) {
      await pool.end();
    }
  }
}

addTestRecords();
