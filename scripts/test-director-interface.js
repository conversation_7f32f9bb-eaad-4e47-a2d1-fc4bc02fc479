const fs = require('fs');
const path = require('path');

console.log('🔍 检查主任界面修改...\n');

// 读取主任界面文件
const directorDashboardPath = path.join(__dirname, '../views/director/dashboard.ejs');
const content = fs.readFileSync(directorDashboardPath, 'utf8');

// 检查是否还包含被移除的内容
const removedSections = [
  '调休类型分布',
  '员工值班统计',
  'totalCustomLeave',
  'totalAutoLeave',
  'customLeaveRate',
  'progress is-primary',
  'progress is-success',
  'progress is-danger'
];

console.log('📋 检查已移除的内容：');
let allRemoved = true;

removedSections.forEach(section => {
  const found = content.includes(section);
  const status = found ? '❌ 仍存在' : '✅ 已移除';
  console.log(`   ${section}: ${status}`);
  
  if (found) {
    allRemoved = false;
  }
});

// 检查保留的重要内容
const preservedSections = [
  '员工管理',
  '值班记录管理',
  '添加记录',
  '批量导入',
  'showAddRecordModal',
  'showImportDutyModal'
];

console.log('\n📋 检查保留的内容：');
let allPreserved = true;

preservedSections.forEach(section => {
  const found = content.includes(section);
  const status = found ? '✅ 已保留' : '❌ 缺失';
  console.log(`   ${section}: ${status}`);
  
  if (!found) {
    allPreserved = false;
  }
});

// 统计行数
const lines = content.split('\n');
console.log(`\n📊 文件统计：`);
console.log(`   总行数: ${lines.length}`);
console.log(`   文件大小: ${(content.length / 1024).toFixed(2)} KB`);

// 检查页面结构
const hasHeader = content.includes('<%- include(\'../partials/header\') %>');
const hasFooter = content.includes('<%- include(\'../partials/footer\') %>');
const hasColumns = content.includes('<div class="columns">');

console.log(`\n🏗️ 页面结构检查：`);
console.log(`   包含头部: ${hasHeader ? '✅' : '❌'}`);
console.log(`   包含尾部: ${hasFooter ? '✅' : '❌'}`);
console.log(`   包含列布局: ${hasColumns ? '✅' : '❌'}`);

// 总结
console.log(`\n🎉 修改总结：`);
if (allRemoved && allPreserved && hasHeader && hasFooter && hasColumns) {
  console.log('   ✅ 所有修改都已正确完成！');
  console.log('   ✅ 不需要的统计部分已移除');
  console.log('   ✅ 重要功能都已保留');
  console.log('   ✅ 页面结构完整');
} else {
  console.log('   ⚠️ 发现一些问题，请检查上述详情');
}

console.log('\n📝 主任界面现在包含：');
console.log('   1. 基础统计卡片（总值班天数、参与人数、平均值班）');
console.log('   2. 员工管理表格（工号、姓名、本月值班、详情按钮）');
console.log('   3. 值班记录管理（年月选择、添加记录、批量导入、记录表格）');
console.log('   4. 完整的编辑和删除功能');

console.log('\n✨ 界面更加简洁，专注于核心功能！');
