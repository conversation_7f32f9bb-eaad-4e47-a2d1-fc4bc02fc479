const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 数据库配置 - 从.env文件读取
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  multipleStatements: true
};

async function quickSetup() {
  let connection;
  
  try {
    console.log('🚀 开始快速设置值班管理系统...\n');
    
    // 1. 连接MySQL
    console.log('📡 连接MySQL服务器...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ MySQL连接成功\n');
    
    // 2. 创建数据库
    console.log('🗄️  创建数据库...');
    await connection.query(`DROP DATABASE IF EXISTS ${process.env.DB_NAME}`);
    await connection.query(`CREATE DATABASE ${process.env.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.query(`USE ${process.env.DB_NAME}`);
    console.log('✅ 数据库创建成功\n');
    
    // 3. 创建用户表
    console.log('👥 创建用户表...');
    const createUsersTable = `
      CREATE TABLE users (
        userid INT PRIMARY KEY AUTO_INCREMENT,
        job_number VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(50) NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'director', 'staff') NOT NULL,
        department VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    await connection.execute(createUsersTable);
    console.log('✅ 用户表创建成功');
    
    // 4. 创建值班记录表
    console.log('📋 创建值班记录表...');
    const createDutyRecordsTable = `
      CREATE TABLE duty_records (
        record_id INT PRIMARY KEY AUTO_INCREMENT,
        userid INT NOT NULL,
        duty_date DATE,
        leave_date DATE,
        leave_end_date DATE,
        is_custom_leave BOOLEAN DEFAULT FALSE,
        is_leave_record BOOLEAN DEFAULT FALSE,
        is_holiday_duty BOOLEAN DEFAULT FALSE,
        remarks TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userid) REFERENCES users(userid) ON DELETE CASCADE
      )
    `;
    await connection.execute(createDutyRecordsTable);
    console.log('✅ 值班记录表创建成功\n');
    
    // 5. 创建默认用户
    console.log('🔐 创建默认用户...');
    
    // 加密密码
    const adminPassword = await bcrypt.hash('admin', 10);
    const directorPassword = await bcrypt.hash('123456', 10);
    const staffPassword = await bcrypt.hash('123456', 10);
    
    // 插入用户数据
    const users = [
      ['admin', '系统管理员', adminPassword, 'admin', '管理部'],
      ['D001', '张主任', directorPassword, 'director', '技术部'],
      ['S001', '李老师', staffPassword, 'staff', '技术部'],
      ['S002', '王老师', staffPassword, 'staff', '技术部'],
      ['S003', '赵老师', staffPassword, 'staff', '技术部']
    ];
    
    for (const user of users) {
      await connection.execute(
        'INSERT INTO users (job_number, name, password, role, department) VALUES (?, ?, ?, ?, ?)',
        user
      );
      console.log(`   ✅ 创建用户: ${user[0]} - ${user[1]} (${user[3]})`);
    }
    
    // 6. 创建示例值班记录
    console.log('\n📅 创建示例值班记录...');
    const dutyRecords = [
      [3, '2024-06-15', '2024-06-16', null, true, false, false, '周末值班'],  // S001 值班
      [4, '2024-06-20', '2024-06-21', null, false, false, false, ''],        // S002 值班
      [5, '2024-06-25', '2024-06-26', null, true, false, false, '临时调班'],  // S003 值班
      [3, '2024-07-01', '2024-07-02', '2024-07-03', true, false, true, '国庆节假日值班'], // S001 节假日值班
      [4, '2024-07-05', null, null, false, false, false, '调休待定'],         // S002 值班(未安排调休)
      [5, null, '2024-08-10', '2024-08-12', false, true, false, '请假'],      // S003 请假
      [3, '2024-12-15', '2024-12-16', null, true, false, false, ''],         // S001 值班
      [4, '2025-01-01', '2025-01-02', '2025-01-03', true, false, true, '元旦节假日值班'], // S002 节假日值班
      [5, '2025-03-20', '2025-03-21', null, true, false, false, '春季值班'],  // S003 值班
      [3, null, '2025-05-01', null, false, true, false, '请假'],             // S001 请假
      [4, '2025-06-05', null, null, false, false, false, '调休安排中'],       // S002 值班(未安排调休)
    ];
    
    for (const record of dutyRecords) {
      await connection.execute(
        'INSERT INTO duty_records (userid, duty_date, leave_date, leave_end_date, is_custom_leave, is_leave_record, is_holiday_duty, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        record
      );
    }
    console.log('✅ 示例值班记录创建成功\n');
    
    // 7. 验证安装
    console.log('🔍 验证安装...');
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [recordCount] = await connection.execute('SELECT COUNT(*) as count FROM duty_records');
    
    console.log(`   👥 用户数量: ${userCount[0].count}`);
    console.log(`   📋 值班记录数量: ${recordCount[0].count}`);
    
    console.log('\n🎉 快速设置完成！\n');
    
    // 8. 显示登录信息
    console.log('📋 默认登录账号:');
    console.log('┌─────────────┬──────────┬──────────┬──────────┐');
    console.log('│    角色     │   工号   │   密码   │   姓名   │');
    console.log('├─────────────┼──────────┼──────────┼──────────┤');
    console.log('│ 系统管理员  │  admin   │  admin   │系统管理员│');
    console.log('│    主任     │  D001    │ 123456   │  张主任  │');
    console.log('│    老师     │  S001    │ 123456   │  李老师  │');
    console.log('│    老师     │  S002    │ 123456   │  王老师  │');
    console.log('│    老师     │  S003    │ 123456   │  赵老师  │');
    console.log('└─────────────┴──────────┴──────────┴──────────┘\n');
    
    console.log('🌐 启动应用:');
    console.log('   node src/app.js');
    console.log('   然后访问: http://localhost:3000\n');
    
  } catch (error) {
    console.error('❌ 设置过程中发生错误:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 解决方案:');
      console.log('   1. 检查MySQL用户名和密码是否正确');
      console.log('   2. 修改此脚本顶部的数据库配置');
      console.log('   3. 确保MySQL服务正在运行');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决方案:');
      console.log('   1. 启动MySQL服务');
      console.log('   2. 检查MySQL是否在默认端口3306运行');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行快速设置
quickSetup();
