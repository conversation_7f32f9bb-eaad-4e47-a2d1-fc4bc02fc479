const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 数据库配置 - 从.env文件读取
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

async function initDatabase() {
  let connection;

  try {
    console.log('🗄️  开始初始化数据库...\n');

    // 连接到数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 连接到数据库成功');

    // 删除现有表（如果存在）- 注意顺序，先删除有外键的表
    console.log('🗑️  清理现有表...');
    await connection.execute('DROP TABLE IF EXISTS duty_records');
    await connection.execute('DROP TABLE IF EXISTS users');
    console.log('✅ 现有表已清理');

    // 创建 users 表
    console.log('👥 创建用户表...');
    await connection.execute(`
      CREATE TABLE users (
        userid INT AUTO_INCREMENT PRIMARY KEY,
        job_number VARCHAR(50) UNIQUE NOT NULL COMMENT '工号',
        name VARCHAR(100) NOT NULL COMMENT '姓名',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        role ENUM('staff', 'director', 'admin') NOT NULL COMMENT '角色',
        department VARCHAR(50) COMMENT '部门',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
      ) COMMENT='用户表'
    `);
    console.log('✅ 用户表创建成功');

    // 创建 duty_records 表
    console.log('📋 创建值班记录表...');
    await connection.execute(`
      CREATE TABLE duty_records (
        record_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
        userid INT NOT NULL COMMENT '用户ID',
        duty_date DATE COMMENT '值班日期（请假记录时可为空）',
        leave_date DATE COMMENT '调休/请假开始日期',
        leave_end_date DATE COMMENT '请假结束日期',
        is_custom_leave BOOLEAN DEFAULT FALSE COMMENT '是否自定义调休',
        is_leave_record BOOLEAN DEFAULT FALSE COMMENT '是否为请假记录',
        is_holiday_duty BOOLEAN DEFAULT FALSE COMMENT '是否为节假日值班',
        remarks TEXT COMMENT '备注',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        FOREIGN KEY (userid) REFERENCES users(userid) ON DELETE CASCADE,
        INDEX idx_userid_duty_date (userid, duty_date),
        INDEX idx_duty_date (duty_date),
        INDEX idx_leave_date (leave_date)
      ) COMMENT='值班记录表'
    `);
    console.log('✅ 值班记录表创建成功');

    // 创建默认用户
    console.log('\n🔐 创建默认用户...');
    const adminPassword = await bcrypt.hash('admin', 10);
    await connection.execute(
      'INSERT INTO users (job_number, name, password, role, department) VALUES (?, ?, ?, ?, ?)',
      ['admin', '系统管理员', adminPassword, 'admin', '管理部']
    );
    console.log('   ✅ 管理员: admin / admin');

    // 创建示例用户
    const users = [
      { job_number: 'D001', name: '张主任', password: '123456', role: 'director', department: '技术部' },
      { job_number: 'S001', name: '李老师', password: '123456', role: 'staff', department: '技术部' },
      { job_number: 'S002', name: '王老师', password: '123456', role: 'staff', department: '技术部' },
      { job_number: 'S003', name: '赵老师', password: '123456', role: 'staff', department: '技术部' }
    ];

    for (const user of users) {
      const hashedPassword = await bcrypt.hash(user.password, 10);
      await connection.execute(
        'INSERT INTO users (job_number, name, password, role, department) VALUES (?, ?, ?, ?, ?)',
        [user.job_number, user.name, hashedPassword, user.role, user.department]
      );
      console.log(`   ✅ ${user.role === 'director' ? '主任' : '老师'}: ${user.job_number} / 123456 (${user.name})`);
    }

    // 创建示例值班记录
    console.log('\n📅 创建示例值班记录...');
    const records = [
      { job_number: 'S001', duty_date: '2024-06-15', leave_date: '2024-06-16', leave_end_date: null, is_custom: true, is_leave: false, is_holiday: false, remarks: '周末值班' },
      { job_number: 'S002', duty_date: '2024-06-20', leave_date: '2024-06-21', leave_end_date: null, is_custom: false, is_leave: false, is_holiday: false, remarks: '' },
      { job_number: 'S003', duty_date: '2024-06-25', leave_date: '2024-06-26', leave_end_date: null, is_custom: true, is_leave: false, is_holiday: false, remarks: '临时调班' },
      { job_number: 'S001', duty_date: '2024-07-01', leave_date: '2024-07-02', leave_end_date: '2024-07-03', is_custom: true, is_leave: false, is_holiday: true, remarks: '国庆节假日值班' },
      { job_number: 'S002', duty_date: '2024-07-05', leave_date: null, leave_end_date: null, is_custom: false, is_leave: false, is_holiday: false, remarks: '调休待定' },
      { job_number: 'S003', duty_date: null, leave_date: '2024-08-10', leave_end_date: '2024-08-12', is_custom: false, is_leave: true, is_holiday: false, remarks: '请假' },
      { job_number: 'S001', duty_date: '2024-12-15', leave_date: '2024-12-16', leave_end_date: null, is_custom: true, is_leave: false, is_holiday: false, remarks: '' },
      { job_number: 'S002', duty_date: '2025-01-01', leave_date: '2025-01-02', leave_end_date: '2025-01-03', is_custom: true, is_leave: false, is_holiday: true, remarks: '元旦节假日值班' },
      { job_number: 'S003', duty_date: '2025-03-20', leave_date: '2025-03-21', leave_end_date: null, is_custom: true, is_leave: false, is_holiday: false, remarks: '春季值班' },
      { job_number: 'S001', duty_date: null, leave_date: '2025-05-01', leave_end_date: null, is_custom: false, is_leave: true, is_holiday: false, remarks: '请假' },
      { job_number: 'S002', duty_date: '2025-06-05', leave_date: null, leave_end_date: null, is_custom: false, is_leave: false, is_holiday: false, remarks: '调休安排中' }
    ];

    for (const record of records) {
      // 获取用户ID
      const [userRows] = await connection.execute(
        'SELECT userid FROM users WHERE job_number = ?',
        [record.job_number]
      );

      if (userRows.length > 0) {
        await connection.execute(
          'INSERT INTO duty_records (userid, duty_date, leave_date, leave_end_date, is_custom_leave, is_leave_record, is_holiday_duty, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          [userRows[0].userid, record.duty_date, record.leave_date, record.leave_end_date, record.is_custom, record.is_leave, record.is_holiday, record.remarks]
        );
        console.log(`   ✅ ${record.job_number}: ${record.duty_date} ${record.leave_date ? '→ ' + record.leave_date : '(未安排调休)'}`);
      }
    }

    console.log('\n🎉 数据库初始化完成！\n');

    console.log('📋 默认登录账号:');
    console.log('┌─────────────┬──────────┬──────────┬──────────┐');
    console.log('│    角色     │   工号   │   密码   │   姓名   │');
    console.log('├─────────────┼──────────┼──────────┼──────────┤');
    console.log('│ 系统管理员  │  admin   │  admin   │系统管理员│');
    console.log('│    主任     │  D001    │ 123456   │  张主任  │');
    console.log('│    老师     │  S001    │ 123456   │  李老师  │');
    console.log('│    老师     │  S002    │ 123456   │  王老师  │');
    console.log('│    老师     │  S003    │ 123456   │  赵老师  │');
    console.log('└─────────────┴──────────┴──────────┴──────────┘\n');

    console.log('🌐 启动应用:');
    console.log('   node src/app.js');
    console.log('   然后访问: http://localhost:3000\n');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);

    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 解决方案:');
      console.log('   1. 检查脚本顶部的数据库配置');
      console.log('   2. 确保MySQL用户名和密码正确');
      console.log('   3. 确保MySQL服务正在运行');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决方案:');
      console.log('   1. 启动MySQL服务');
      console.log('   2. 检查MySQL是否在默认端口3306运行');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 解决方案:');
      console.log('   1. 先创建数据库: CREATE DATABASE duty_system;');
      console.log('   2. 或者使用 quick-setup.js 自动创建');
    }

    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行初始化
initDatabase();
