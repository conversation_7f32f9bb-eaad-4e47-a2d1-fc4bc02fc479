<%- include('../partials/header') %>

<div class="columns">
    <div class="column">
        <h2 class="title is-2">主任管理面板</h2>
        <p class="has-text-grey">当前查看：<%= currentYear %>年<%= currentMonth %>月</p>
    </div>
</div>

<!-- 主要统计卡片 -->
<div class="columns is-multiline mb-4">
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">馆员总数</h5>
                <h3 class="title is-3 has-text-primary"><%= stats.totalStaff %></h3>
                <p class="subtitle is-6">部门人员</p>
            </div>
        </div>
    </div>
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">本月值班</h5>
                <h3 class="title is-3 has-text-success"><%= stats.totalDutyDays %></h3>
                <p class="subtitle is-6">天数</p>
            </div>
        </div>
    </div>
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">参与人数</h5>
                <h3 class="title is-3 has-text-info"><%= stats.uniqueStaff %></h3>
                <p class="subtitle is-6">/ <%= stats.totalStaff %> 人</p>
            </div>
        </div>
    </div>

</div>



<div class="columns">
    <div class="column is-one-third">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">馆员管理</h5>
                    </div>
                    <div class="level-right">
                        <small class="has-text-grey" style="margin-left: 1rem;">共 <%= stats.totalStaff %> 人</small>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <div class="table-container">
                    <table class="table is-striped is-narrow is-fullwidth">
                        <thead>
                            <tr>
                                <th>工号</th>
                                <th>姓名</th>
                                <th>本月值班</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% staffStats.forEach(member => { %>
                            <tr>
                                <td><%= member.job_number %></td>
                                <td><%= member.name %></td>
                                <td>
                                    <% if (member.dutyCount > 0) { %>
                                        <span class="tag is-primary"><%= member.dutyCount %></span>
                                    <% } else { %>
                                        <span class="tag is-light">0</span>
                                    <% } %>
                                </td>
                                <td>
                                    <button class="button is-info is-outlined is-small" onclick="viewStaffDetail(<%= member.userid %>, '<%= member.name %>')">详情</button>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="column is-two-thirds">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">值班记录管理</h5>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <div class="control">
                                <div class="select is-small">
                                    <select id="yearSelect">
                                        <%
                                        const startYear = 2024;
                                        const endYear = new Date().getFullYear() + 1;
                                        for(let year = endYear; year >= startYear; year--) { %>
                                        <option value="<%= year %>" <%= year === currentYear ? 'selected' : '' %>><%= year %>年</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                            <div class="control">
                                <div class="select is-small">
                                    <select id="monthSelect">
                                        <% for(let i = 1; i <= 12; i++) { %>
                                        <option value="<%= i %>" <%= i === currentMonth ? 'selected' : '' %>><%= i %>月</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                            <div class="control">
                                <button class="button is-primary is-small" onclick="showAddRecordModal()">添加记录</button>
                            </div>
                            <div class="control">
                                <button class="button is-success is-small" onclick="showImportDutyModal()">批量导入</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <% if (records.length > 0) { %>
                <div class="table-container">
                    <table class="table is-striped is-narrow is-fullwidth">
                        <thead>
                            <tr>
                                <th>工号</th>
                                <th>姓名</th>
                                <th>值班日期</th>
                                <th>调休日期</th>
                                <th>是否自定义调休</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% records.forEach(record => { %>
                            <tr>
                                <td><%= record.job_number %></td>
                                <td><%= record.name %></td>
                                <td>
                                    <% if (record.is_leave_record) { %>
                                        <span class="tag is-warning">请假</span>
                                    <% } else if (record.duty_date) { %>
                                        <%= (() => {
                                            const date = new Date(record.duty_date);
                                            const year = date.getFullYear();
                                            const month = String(date.getMonth() + 1).padStart(2, '0');
                                            const day = String(date.getDate()).padStart(2, '0');
                                            return `${year}-${month}-${day}`;
                                        })() %>
                                    <% } else { %>
                                        <span class="has-text-grey">-</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (record.leave_date) { %>
                                        <%= (() => {
                                            const date = new Date(record.leave_date);
                                            const year = date.getFullYear();
                                            const month = String(date.getMonth() + 1).padStart(2, '0');
                                            const day = String(date.getDate()).padStart(2, '0');
                                            return `${year}-${month}-${day}`;
                                        })() %>
                                        <% if (record.leave_end_date) { %>
                                            至 <%= (() => {
                                                const date = new Date(record.leave_end_date);
                                                const year = date.getFullYear();
                                                const month = String(date.getMonth() + 1).padStart(2, '0');
                                                const day = String(date.getDate()).padStart(2, '0');
                                                return `${year}-${month}-${day}`;
                                            })() %>
                                        <% } %>
                                    <% } else { %>
                                        未设置
                                    <% } %>
                                </td>
                                <td>
                                    <% if (record.is_leave_record) { %>
                                        <span class="tag is-light">请假记录</span>
                                    <% } else if (record.is_holiday_duty) { %>
                                        <span class="tag is-warning">节假日值班</span>
                                    <% } else { %>
                                        <%= record.is_custom_leave ? '是' : '否' %>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (record.remarks && record.remarks.trim()) { %>
                                        <span class="has-text-grey" title="<%= record.remarks %>">
                                            <%= record.remarks.length > 20 ? record.remarks.substring(0, 20) + '...' : record.remarks %>
                                        </span>
                                    <% } else { %>
                                        <span class="has-text-grey-light">-</span>
                                    <% } %>
                                </td>
                                <td>
                                    <div class="field is-grouped">
                                        <p class="control">
                                            <button class="button is-primary is-outlined is-small" onclick="editRecord(<%= record.record_id %>)">编辑</button>
                                        </p>
                                        <p class="control">
                                            <button class="button is-danger is-outlined is-small" onclick="deleteRecord(<%= record.record_id %>)">删除</button>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
                <% } else { %>
                <p class="has-text-centered has-text-grey">本月暂无值班记录</p>
                <% } %>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
