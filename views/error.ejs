<%- include('partials/header') %>

<div class="columns is-centered">
    <div class="column is-half has-text-centered">
        <div class="card">
            <div class="card-content">
                <h1 class="title is-3 has-text-danger">系统错误</h1>
                <p class="has-text-grey">抱歉，系统发生了错误。</p>
                <% if (typeof error !== 'undefined' && error.message) { %>
                <div class="notification is-danger has-text-left">
                    <strong>错误信息：</strong><%= error.message %>
                    <% if (error.stack) { %>
                    <pre class="mt-2"><%= error.stack %></pre>
                    <% } %>
                </div>
                <% } %>
                <a href="/" class="button is-primary">返回首页</a>
            </div>
        </div>
    </div>
</div>

<%- include('partials/footer') %>
