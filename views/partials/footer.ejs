    </div>
    <footer class="footer has-background-light has-text-centered has-text-grey mt-5">
        <div class="container">
            <p>&copy; 2024 值班管理系统. All rights reserved.</p>
        </div>
    </footer>
    <script src="/js/main.js"></script>
    <script>
        // 角色切换函数
        function switchRole(newRole) {
            if (confirm('确定要切换到该角色吗？页面将会刷新。')) {
                fetch('/switch-role', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ newRole: newRole })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示切换成功提示
                        const roleNames = {
                            'admin': '管理员',
                            'director': '主任',
                            'staff': '员工'
                        };

                        // 可以添加一个简单的提示
                        const notification = document.createElement('div');
                        notification.className = 'notification is-success is-light';
                        notification.style.position = 'fixed';
                        notification.style.top = '20px';
                        notification.style.right = '20px';
                        notification.style.zIndex = '9999';
                        notification.innerHTML = `
                            <button class="delete" onclick="this.parentElement.remove()"></button>
                            已切换到${roleNames[newRole]}身份
                        `;
                        document.body.appendChild(notification);

                        // 1秒后跳转
                        setTimeout(() => {
                            window.location.href = data.redirectUrl;
                        }, 1000);
                    } else {
                        alert('切换角色失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('切换角色时发生错误');
                });
            }
        }
    </script>
</body>
</html>
