<%- include('../partials/header') %>

<div class="columns is-centered">
    <div class="column is-half">
        <div class="card">
            <div class="card-header">
                <h4 class="title is-4">修改密码</h4>
            </div>
            <div class="card-content">
                <% if (error) { %>
                <div class="notification is-danger">
                    <%= error %>
                </div>
                <% } %>

                <% if (success) { %>
                <div class="notification is-success">
                    <%= success %>
                </div>
                <% } %>

                <form method="POST" action="/change-password">
                    <div class="field">
                        <label class="label" for="current_password">当前密码</label>
                        <div class="control">
                            <input class="input" type="password" id="current_password" name="current_password" required>
                        </div>
                    </div>
                    <div class="field">
                        <label class="label" for="new_password">新密码</label>
                        <div class="control">
                            <input class="input" type="password" id="new_password" name="new_password" required
                                   oninput="checkPasswordStrength()" minlength="8">
                        </div>
                        <div id="password-strength" class="help"></div>
                        <div class="help">
                            <p class="has-text-grey-dark">密码要求：</p>
                            <ul class="has-text-grey-dark" style="margin-left: 1rem;">
                                <li id="length-check">• 至少8位字符</li>
                                <li id="uppercase-check">• 包含大写字母</li>
                                <li id="lowercase-check">• 包含小写字母</li>
                                <li id="number-check">• 包含数字</li>
                            </ul>
                        </div>
                    </div>
                    <div class="field">
                        <label class="label" for="confirm_password">确认新密码</label>
                        <div class="control">
                            <input class="input" type="password" id="confirm_password" name="confirm_password" required
                                   oninput="checkPasswordMatch()">
                        </div>
                        <div id="password-match" class="help"></div>
                    </div>
                    <div class="field is-grouped is-grouped-right">
                        <div class="control">
                            <a href="/" class="button is-light">返回</a>
                        </div>
                        <div class="control">
                            <button type="submit" class="button is-primary" id="submit-btn" disabled>修改密码</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function checkPasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthDiv = document.getElementById('password-strength');
    const submitBtn = document.getElementById('submit-btn');

    // 检查各项要求
    const hasLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);

    // 更新检查项显示
    updateCheckItem('length-check', hasLength);
    updateCheckItem('uppercase-check', hasUppercase);
    updateCheckItem('lowercase-check', hasLowercase);
    updateCheckItem('number-check', hasNumber);

    // 计算强度
    const allValid = hasLength && hasUppercase && hasLowercase && hasNumber;

    if (password.length === 0) {
        strengthDiv.innerHTML = '';
        submitBtn.disabled = true;
    } else if (allValid) {
        strengthDiv.innerHTML = '<span class="has-text-success">✓ 密码强度符合要求</span>';
        checkPasswordMatch(); // 重新检查密码匹配
    } else {
        strengthDiv.innerHTML = '<span class="has-text-danger">✗ 密码强度不符合要求</span>';
        submitBtn.disabled = true;
    }
}

function updateCheckItem(id, isValid) {
    const element = document.getElementById(id);
    if (isValid) {
        element.className = 'has-text-success';
        element.innerHTML = element.innerHTML.replace('•', '✓');
    } else {
        element.className = 'has-text-grey-dark';
        element.innerHTML = element.innerHTML.replace('✓', '•');
    }
}

function checkPasswordMatch() {
    const password = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchDiv = document.getElementById('password-match');
    const submitBtn = document.getElementById('submit-btn');

    // 检查密码强度
    const hasLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const isStrong = hasLength && hasUppercase && hasLowercase && hasNumber;

    if (confirmPassword.length === 0) {
        matchDiv.innerHTML = '';
        submitBtn.disabled = true;
    } else if (password === confirmPassword) {
        matchDiv.innerHTML = '<span class="has-text-success">✓ 密码匹配</span>';
        submitBtn.disabled = !isStrong; // 只有密码强度也符合要求才启用提交按钮
    } else {
        matchDiv.innerHTML = '<span class="has-text-danger">✗ 密码不匹配</span>';
        submitBtn.disabled = true;
    }
}

// 表单提交前最终验证
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    // 验证密码强度
    const hasLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);

    if (!hasLength || !hasUppercase || !hasLowercase || !hasNumber) {
        e.preventDefault();
        alert('密码必须至少8位，并包含大小写字母和数字');
        return false;
    }

    if (password !== confirmPassword) {
        e.preventDefault();
        alert('新密码与确认密码不匹配');
        return false;
    }
});
</script>

<%- include('../partials/footer') %>
