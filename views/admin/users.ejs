<%- include('../partials/header') %>

<div class="columns">
    <div class="column">
        <div class="level mb-4">
            <div class="level-left">
                <h2 class="title is-2">用户管理</h2>
            </div>
            <div class="level-right">
                <div class="field is-grouped">
                    <p class="control">
                        <a href="/admin/dashboard" class="button is-light">返回仪表板</a>
                    </p>
                    <p class="control">
                        <button class="button is-success" onclick="showImportModal()">批量导入</button>
                    </p>
                    <p class="control">
                        <button class="button is-primary" onclick="showAddUserModal()">添加用户</button>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="columns">
    <div class="column">
        <div class="card">
            <div class="card-header">
                <h5 class="title is-5">用户列表</h5>
            </div>
            <div class="card-content">
                <div class="table-container">
                    <table class="table is-striped is-fullwidth">
                        <thead>
                            <tr>
                                <th>用户ID</th>
                                <th>工号</th>
                                <th>姓名</th>
                                <th>角色</th>
                                <th>角色数量</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% users.all.forEach(user => { %>
                            <tr>
                                <td><%= user.userid %></td>
                                <td><%= user.job_number %></td>
                                <td><%= user.name %></td>
                                <td>
                                    <% if (user.roles && user.roles.length > 0) { %>
                                        <% user.roles.forEach((roleInfo, index) => { %>
                                            <% if (roleInfo.role === 'admin') { %>
                                                <span class="tag is-danger <%= roleInfo.is_primary ? '' : 'is-light' %>">
                                                    <% if (roleInfo.is_primary) { %><i class="fas fa-star" style="margin-right: 0.25rem;"></i><% } %>
                                                    管理员
                                                </span>
                                            <% } else if (roleInfo.role === 'director') { %>
                                                <span class="tag is-warning <%= roleInfo.is_primary ? '' : 'is-light' %>">
                                                    <% if (roleInfo.is_primary) { %><i class="fas fa-star" style="margin-right: 0.25rem;"></i><% } %>
                                                    主任
                                                </span>
                                            <% } else { %>
                                                <span class="tag is-info <%= roleInfo.is_primary ? '' : 'is-light' %>">
                                                    <% if (roleInfo.is_primary) { %><i class="fas fa-star" style="margin-right: 0.25rem;"></i><% } %>
                                                    馆员
                                                </span>
                                            <% } %>
                                            <% if (index < user.roles.length - 1) { %> <% } %>
                                        <% }) %>
                                    <% } else { %>
                                        <% if (user.role === 'admin') { %>
                                        <span class="tag is-danger">管理员</span>
                                        <% } else if (user.role === 'director') { %>
                                        <span class="tag is-warning">主任</span>
                                        <% } else { %>
                                        <span class="tag is-info">馆员</span>
                                        <% } %>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (user.roleCount > 1) { %>
                                        <span class="tag is-primary">
                                            <i class="fas fa-users" style="margin-right: 0.25rem;"></i>
                                            <%= user.roleCount %>
                                        </span>
                                    <% } else { %>
                                        <span class="tag is-light">1</span>
                                    <% } %>
                                </td>
                                <td><%= user.created_at ? (() => {
                                    const date = new Date(user.created_at);
                                    const year = date.getFullYear();
                                    const month = String(date.getMonth() + 1).padStart(2, '0');
                                    const day = String(date.getDate()).padStart(2, '0');
                                    return `${year}-${month}-${day}`;
                                })() : '未知' %></td>
                                <td>
                                    <div class="field is-grouped">
                                        <% if (user.job_number !== 'admin') { %>
                                        <p class="control">
                                            <button class="button is-primary is-outlined is-small" onclick="editUser(<%= user.userid %>)" title="编辑用户">
                                                <span class="icon is-small">
                                                    <i class="fas fa-edit"></i>
                                                </span>
                                                <span>编辑</span>
                                            </button>
                                        </p>
                                        <p class="control">
                                            <button class="button is-warning is-outlined is-small" onclick="resetPassword(<%= user.userid %>)" title="重置密码">
                                                <span class="icon is-small">
                                                    <i class="fas fa-key"></i>
                                                </span>
                                                <span>重置密码</span>
                                            </button>
                                        </p>
                                        <% } else { %>
                                        <p class="control">
                                            <span class="tag is-light">
                                                <i class="fas fa-shield-alt" style="margin-right: 0.25rem;"></i>
                                                系统默认管理员
                                            </span>
                                        </p>
                                        <% } %>
                                        <% if (user.userid != locals.user.userid && user.job_number !== 'admin') { %>
                                        <p class="control">
                                            <button class="button is-danger is-outlined is-small" onclick="deleteUser(<%= user.userid %>)" title="删除用户">
                                                <span class="icon is-small">
                                                    <i class="fas fa-trash"></i>
                                                </span>
                                                <span>删除</span>
                                            </button>
                                        </p>
                                        <% } %>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="columns is-multiline mt-4">
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">总用户数</h5>
                <h3 class="title is-3 has-text-primary"><%= users.all.length %></h3>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">管理员</h5>
                <h3 class="title is-3 has-text-danger"><%= users.admins.length %></h3>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">主任</h5>
                <h3 class="title is-3 has-text-warning"><%= users.directors.length %></h3>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">馆员</h5>
                <h3 class="title is-3 has-text-info"><%= users.staff.length %></h3>
            </div>
        </div>
    </div>
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">多角色用户</h5>
                <h3 class="title is-3 has-text-success">
                    <i class="fas fa-users"></i>
                    <%= users.multiRole ? users.multiRole.length : 0 %>
                </h3>
                <p class="subtitle is-6 has-text-grey">
                    拥有多个角色的用户
                </p>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
