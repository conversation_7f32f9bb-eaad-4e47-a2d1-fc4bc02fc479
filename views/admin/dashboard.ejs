<%- include('../partials/header') %>

<div class="columns">
    <div class="column">
        <h2 class="title is-2">系统管理面板</h2>
        <p class="has-text-grey">当前查看：<%= currentYear %>年<%= currentMonth %>月</p>
    </div>
</div>

<div class="columns is-multiline mb-4">
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">总用户数</h5>
                <h3 class="title is-3 has-text-primary"><%= stats.totalUsers %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">馆员数</h5>
                <h3 class="title is-3 has-text-info"><%= stats.totalStaff %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">主任数</h5>
                <h3 class="title is-3 has-text-warning"><%= stats.totalDirectors %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">管理员数</h5>
                <h3 class="title is-3 has-text-danger"><%= stats.totalAdmins %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">本月值班</h5>
                <h3 class="title is-3 has-text-success"><%= stats.totalDutyDays %></h3>
                <p class="subtitle is-6">天</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">平均值班</h5>
                <h3 class="title is-3 has-text-grey"><%= stats.avgDutyPerStaff %></h3>
                <p class="subtitle is-6">天/人</p>
            </div>
        </div>
    </div>
</div>

<!-- 多角色用户统计 -->
<div class="columns mb-4">
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">多角色用户</h5>
                <h3 class="title is-3 has-text-success">
                    <i class="fas fa-users"></i>
                    <%= users.multiRole ? users.multiRole.length : 0 %>
                </h3>
                <p class="subtitle is-6 has-text-grey">
                    拥有多个角色的用户
                </p>
            </div>
        </div>
    </div>
    <div class="column is-9">
        <div class="card">
            <div class="card-content">
                <h6 class="title is-6">多角色用户详情</h6>
                <% if (users.multiRole && users.multiRole.length > 0) { %>
                    <div class="tags">
                        <% users.multiRole.forEach(user => { %>
                            <span class="tag is-medium">
                                <strong><%= user.name %></strong>
                                <span style="margin-left: 0.5rem;">
                                    <% user.roles.forEach((roleInfo, index) => { %>
                                        <% const roleDisplay = roleInfo.role === 'admin' ? '管理员' :
                                                              roleInfo.role === 'director' ? '主任' : '馆员' %>
                                        <%= roleDisplay %><% if (roleInfo.is_primary) { %>⭐<% } %><% if (index < user.roles.length - 1) { %> + <% } %>
                                    <% }) %>
                                </span>
                            </span>
                        <% }) %>
                    </div>
                <% } else { %>
                    <p class="has-text-grey">暂无多角色用户</p>
                <% } %>
            </div>
        </div>
    </div>
</div>

<div class="columns">
    <div class="column is-half">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">用户管理</h5>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <p class="control">
                                <a href="/admin/users-page" class="button is-primary is-outlined is-small">管理用户</a>
                            </p>
                            <p class="control">
                                <button class="button is-success is-small" onclick="showImportModal()">批量导入</button>
                            </p>
                            <p class="control">
                                <button class="button is-primary is-small" onclick="showAddUserModal()">添加用户</button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <div class="table-container">
                    <table class="table is-striped is-narrow is-fullwidth">
                        <thead>
                            <tr>
                                <th>工号</th>
                                <th>姓名</th>
                                <th>角色</th>
                                <th>角色数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% users.all.forEach(user => { %>
                            <tr>
                                <td><%= user.job_number %></td>
                                <td><%= user.name %></td>
                                <td>
                                    <% if (user.roles && user.roles.length > 0) { %>
                                        <% user.roles.forEach((roleInfo, index) => { %>
                                            <% if (roleInfo.role === 'admin') { %>
                                                <span class="tag is-danger <%= roleInfo.is_primary ? '' : 'is-light' %>">
                                                    <% if (roleInfo.is_primary) { %><i class="fas fa-star" style="margin-right: 0.25rem;"></i><% } %>
                                                    管理员
                                                </span>
                                            <% } else if (roleInfo.role === 'director') { %>
                                                <span class="tag is-warning <%= roleInfo.is_primary ? '' : 'is-light' %>">
                                                    <% if (roleInfo.is_primary) { %><i class="fas fa-star" style="margin-right: 0.25rem;"></i><% } %>
                                                    主任
                                                </span>
                                            <% } else { %>
                                                <span class="tag is-info <%= roleInfo.is_primary ? '' : 'is-light' %>">
                                                    <% if (roleInfo.is_primary) { %><i class="fas fa-star" style="margin-right: 0.25rem;"></i><% } %>
                                                    馆员
                                                </span>
                                            <% } %>
                                            <% if (index < user.roles.length - 1) { %> <% } %>
                                        <% }) %>
                                    <% } else { %>
                                        <% if (user.role === 'admin') { %>
                                        <span class="tag is-danger">管理员</span>
                                        <% } else if (user.role === 'director') { %>
                                        <span class="tag is-warning">主任</span>
                                        <% } else { %>
                                        <span class="tag is-info">馆员</span>
                                        <% } %>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (user.roleCount > 1) { %>
                                        <span class="tag is-primary">
                                            <i class="fas fa-users" style="margin-right: 0.25rem;"></i>
                                            <%= user.roleCount %>
                                        </span>
                                    <% } else { %>
                                        <span class="tag is-light">1</span>
                                    <% } %>
                                </td>
                                <td>
                                    <div class="field is-grouped">
                                        <% if (user.job_number !== 'admin') { %>
                                        <p class="control">
                                            <button class="button is-primary is-outlined is-small" onclick="editUser(<%= user.userid %>)">编辑</button>
                                        </p>
                                        <p class="control">
                                            <button class="button is-warning is-outlined is-small" onclick="resetPassword(<%= user.userid %>)">重置密码</button>
                                        </p>
                                        <% } else { %>
                                        <p class="control">
                                            <span class="tag is-light">
                                                <i class="fas fa-shield-alt" style="margin-right: 0.25rem;"></i>
                                                系统默认
                                            </span>
                                        </p>
                                        <% } %>
                                        <% if (user.userid != locals.user.userid && user.job_number !== 'admin') { %>
                                        <p class="control">
                                            <button class="button is-danger is-outlined is-small" onclick="deleteUser(<%= user.userid %>)">删除</button>
                                        </p>
                                        <% } %>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="column is-half">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">值班记录</h5>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <div class="control">
                                <div class="select is-small">
                                    <select id="yearSelect">
                                        <%
                                        const startYear = 2024;
                                        const endYear = new Date().getFullYear() + 1;
                                        for(let year = endYear; year >= startYear; year--) { %>
                                        <option value="<%= year %>" <%= year === currentYear ? 'selected' : '' %>><%= year %>年</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                            <div class="control">
                                <div class="select is-small">
                                    <select id="monthSelect">
                                        <% for(let i = 1; i <= 12; i++) { %>
                                        <option value="<%= i %>" <%= i === currentMonth ? 'selected' : '' %>><%= i %>月</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <% if (records.length > 0) { %>
                <div class="table-container">
                    <table class="table is-striped is-narrow is-fullwidth">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>值班日期</th>
                                <th>调休日期</th>
                                <th>是否自定义调休</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% records.slice(0, 10).forEach(record => { %>
                            <tr>
                                <td><%= record.name %></td>
                                <td>
                                    <% if (record.is_leave_record) { %>
                                        <span class="tag is-warning">请假</span>
                                    <% } else if (record.duty_date) { %>
                                        <%= (() => {
                                            const date = new Date(record.duty_date);
                                            const year = date.getFullYear();
                                            const month = String(date.getMonth() + 1).padStart(2, '0');
                                            const day = String(date.getDate()).padStart(2, '0');
                                            return `${year}-${month}-${day}`;
                                        })() %>
                                    <% } else { %>
                                        <span class="has-text-grey">-</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (record.leave_date) { %>
                                        <%= (() => {
                                            const date = new Date(record.leave_date);
                                            const year = date.getFullYear();
                                            const month = String(date.getMonth() + 1).padStart(2, '0');
                                            const day = String(date.getDate()).padStart(2, '0');
                                            return `${year}-${month}-${day}`;
                                        })() %>
                                        <% if (record.leave_end_date) { %>
                                            至 <%= (() => {
                                                const date = new Date(record.leave_end_date);
                                                const year = date.getFullYear();
                                                const month = String(date.getMonth() + 1).padStart(2, '0');
                                                const day = String(date.getDate()).padStart(2, '0');
                                                return `${year}-${month}-${day}`;
                                            })() %>
                                        <% } %>
                                    <% } else { %>
                                        未设置
                                    <% } %>
                                </td>
                                <td>
                                    <% if (record.is_leave_record) { %>
                                        <span class="tag is-light">请假记录</span>
                                    <% } else if (record.is_holiday_duty) { %>
                                        <span class="tag is-warning">节假日值班</span>
                                    <% } else { %>
                                        <%= record.is_custom_leave ? '是' : '否' %>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (record.remarks && record.remarks.trim()) { %>
                                        <span class="has-text-grey" title="<%= record.remarks %>">
                                            <%= record.remarks.length > 20 ? record.remarks.substring(0, 20) + '...' : record.remarks %>
                                        </span>
                                    <% } else { %>
                                        <span class="has-text-grey-light">-</span>
                                    <% } %>
                                </td>
                                <td>
                                    <div class="field is-grouped">
                                        <p class="control">
                                            <button class="button is-primary is-outlined is-small" onclick="editRecord(<%= record.record_id %>)">编辑</button>
                                        </p>
                                        <p class="control">
                                            <button class="button is-danger is-outlined is-small" onclick="deleteRecord(<%= record.record_id %>)">删除</button>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
                <% if (records.length > 10) { %>
                <p class="has-text-grey has-text-centered">显示前10条记录，共<%= records.length %>条</p>
                <% } %>
                <% } else { %>
                <p class="has-text-centered has-text-grey">本月暂无值班记录</p>
                <% } %>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
